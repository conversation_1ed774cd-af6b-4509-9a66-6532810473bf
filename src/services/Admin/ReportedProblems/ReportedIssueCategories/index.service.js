import {AppAPIRequest} from "../../../axios";
import { logger } from "../../../../utils";
import  ReportedIssueCategories  from "../../../../apiEndPoints/Admin/ReportedProblems/ReportedIssueCategories/index";

export const AdminReportIssueCategoriesServices = {
  /**
   * Function to get all issue categories
   * @returns {Promise} API response
   */
  getAllCategories: async () => {
    try {
      const payload = ReportedIssueCategories.getAllCategories();
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },

  /**
   * Function to create a new issue category
   * @param {Object} bodyData - The category data to create
   * @returns {Promise} API response
   */
  createCategory: async (bodyData) => {
    try {
      const payload = {
        ...ReportedIssueCategories.createCategory(),
        bodyData
      };
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw new Error(`Failed to create category: ${error.message}`);
    }
  },

  /**
   * Function to delete a category by ID
   * @param {string|number} categoryId - The ID of the category to delete
   * @returns {Promise} API response
   */
  deleteCategory: async (categoryId) => {
    try {
      const payload = ReportedIssueCategories.deleteCategory(categoryId);
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw new Error(`Failed to delete category: ${error.message}`);
    }
  },
};