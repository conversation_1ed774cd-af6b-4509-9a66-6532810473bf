
import ReportStatuses from "../../../../apiEndPoints/Admin/ReportedProblems/ReportStatuses";
import { logger } from "../../../../utils";
import { AppAPIRequest } from "../../../axios";

export const AdminReportStatusesServices = {
  /**
   * Function to get all statuses
   * @returns {Promise} API response
   */
  getAllStatuses: async () => {
    try {
      const payload = ReportStatuses.getAllStatuses();
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },

  /**
   * Function to create a new status
   * @param {Object} bodyData - The status data to create
   * @returns {Promise} API response
   */
  createStatus: async (bodyData) => {
    try {
       
      const payload = {
        ...ReportStatuses.createStatus(),
        bodyData,
      };
      //  console.log("22222222222222222",payload);
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw new Error(`Failed to create status: ${error.message}`);
    }
  },

  /**
   * Function to delete a status by ID
   * @param {string|number} statusId - The ID of the status to delete
   * @returns {Promise} API response
   */
  deleteStatus: async (statusId) => {
    try {
      const payload = ReportStatuses.deleteStatus(statusId);
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw new Error(`Failed to delete status: ${error.message}`);
    }
  },
};