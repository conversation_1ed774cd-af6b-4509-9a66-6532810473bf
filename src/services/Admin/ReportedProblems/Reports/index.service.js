import Reports from "../../../../apiEndPoints/Admin/ReportedProblems/Reports";
import { logger } from "../../../../utils";
import {AppAPIRequest} from "../../../axios";

export const AdminReportsServices = {
  /**
   * Function to get all reports
   * @param {Object} queryParams - Query parameters and pagination options
   * @returns {Promise} API response
   */
  getAllReports: async (queryParams = {}) => {
    try {    
      const payload = {
        ...Reports.getAllReports,
        queryParams, // This should be the direct params object
      };     
      const res = await AppAPIRequest(payload);

      return res;
    } catch (error) {
      console.error('❌ Service: Error in getAllReports:', error);
      logger(error);
      throw error;
    }
  },

  /**
   * Function to get a specific report by ID
   * @param {string} reportId - The ID of the report to fetch
   * @returns {Promise} API response
   */
  getReportById: async (reportId) => {
    try {
      // Correctly call the function to get the configured URL and method
      console.log("🔧 Service: getReportById called with reportId:", reportId);
      const payload = Reports.getReportById(reportId);
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },

  /**
   * Function to update a report by ID
   * @param {string} reportId - The ID of the report to update
   * @param {Object} bodyData - The data to update the report with
   * @returns {Promise} API response
   */
  updateReport: async (reportId, bodyData) => {
    try {
      const payload = {
        ...Reports.updateReport(reportId), // Use the configured endpoint
        bodyData, // Include the body data for the update
      };

      const res = await AppAPIRequest(payload); // Use the APIrequest utility
      return res;
    } catch (error) {
      logger(error); // Log the error
      throw new Error(`Failed to update report: ${error.message}`);
    }
  },

  /**
   * Function to get all statuses
   * @returns {Promise} API response
   */
  getStatuses: async () => {
    try {
      const payload = Reports.getStatuses(); // You'll need to add this to your Reports config
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw new Error(`Failed to get statuses: ${error.message}`);
    }
  },

  /**
   * Function to get all issue categories
   * @returns {Promise} API response
   */
  getIssueCategories: async () => {
    try {
      const payload = Reports.getIssueCategories(); // You'll need to add this to your Reports config
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw new Error(`Failed to get issue categories: ${error.message}`);
    }
  },

  /**
   * Function to send issue resolution email
   * @param {Object} emailData - The email data containing user_email, issue_title, issue_description, resolution_details, admin_name
   * @returns {Promise} API response
   */
  sendIssueResolutionEmail: async (emailData) => {
    try {
      const payload = {
        ...Reports.sendIssueResolutionEmail(),
        bodyData: emailData,
      };
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw new Error(`Failed to send issue resolution email: ${error.message}`);
    }
  },
};