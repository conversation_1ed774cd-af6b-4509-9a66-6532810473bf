const Reports = {
  getAllReports: {
    url: "/admin/reports",
    method: "GET",
  },
  getReportById: (id) => ({
    url: `/admin/reports/${id}`,
    method: "GET",
  }),

  updateReport: (id) => ({
    url: `/admin/reports/${id}`,
    method: "PUT",
  }),
  deleteReport: {
    url: "/admin/reports/:id",
    method: "DELETE",
  },
  getStatuses: () => ({
    url: "/admin/statuses",
    method: "GET",
  }),
  getIssueCategories: () => ({
    url: "/admin/issue-categories",
    method: "GET",
  }),
  sendIssueResolutionEmail: () => ({
    url: "/admin/issue-resolution-email",
    method: "POST",
  }),
};

export default Reports;