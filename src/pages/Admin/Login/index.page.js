import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import { useDispatch } from "react-redux";
// import { useGoogleReCaptcha } from "react-google-recaptcha-v3";

import adminRoutesMap from "../../../routeControl/adminRoutes";
import {
  logger,
  modalNotification,
  setLocalStorageToken,
  // encryptCryptoJsValue
} from "../../../utils";
import { AdminAuthServices } from "../../../services/Admin";
import { login } from "../../../redux/AuthSlice/index.slice";
import { AdminLoginForm } from "../../../components";
import OtpVerificationForm from "../../../components/Form/Admin/OtpVerification/index.form";

function Login() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [userVerified, setUserVerified] = useState(false);
  const [otp, setOtp] = React.useState({
    input1: "",
    input2: "",
    input3: "",
    input4: "",
    input5: "",
    input6: "",
  });
  const [requestId, setRequestId] = useState("");
  const [resetPwd, setResetPwd]=useState(false);
  const [loginData, setLoginData]=useState({});
  // const { executeRecaptcha } = useGoogleReCaptcha();

  const sendOtp = async (Aemail) => {
    try {
      const payload = { email: Aemail, login_type: "LOGIN" };
      const response = await AdminAuthServices.sendEmailOTPService(payload);
      const { success, data, message } = response;
      if (success === 1) {
        const resData = data;
        modalNotification({
          type: "success",
          message,
        });
        setRequestId(resData.request_id);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
  }

  // const sendOtp = async (aEmail) => {
  //   try {
  //     // Adjust the payload for the new API
  //     const payload = { email: aEmail, login_type: "login" };
  
  //     // Send a POST request to the new API
  //     const response = await fetch("https://stag-webapi.daakia.co.in/v2.0/verify_user", {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify(payload),
  //     });
  
  //     // Parse the JSON response
  //     const data = await response.json();
  
  //     // Destructure response data
  //     const { success, message } = data;
  
  //     if (success === 1) {
  //       modalNotification({
  //         type: "success",
  //         message,
  //       });
  //     } else {
  //       modalNotification({
  //         type: "error",
  //         message,
  //       });
  //     }
  //   } catch (error) {
  //     // Log errors
  //     logger(error);
  //   }
  // };  

  const onSubmit = async (values) => {
    setLoading(true);
    try {
      let bodyData = { ...values.email };
      // const token = await executeRecaptcha("admin_login");
      // const encryptedData = {
      //   email: encryptCryptoJsValue(bodyData.email, process.env.REACT_APP_ADMIN_LOGIN_ENCRYPT_SALT_KEY),
      //   password: encryptCryptoJsValue(bodyData.password, process.env.REACT_APP_ADMIN_LOGIN_ENCRYPT_SALT_KEY),
      //   recaptcha_token: token
      // };
      const encryptedData = {
        email: bodyData.email,
        password : bodyData.password,
      }
      const response = await AdminAuthServices.userLogin(encryptedData);
      const { success, data, message } = response;
      if (success === 1) {
        const resData = data;
        resData.userRole = "admin";
        setLoginData(resData);
        modalNotification({
          type: "success",
          message: "Email and Password Verified",
        });
        setUserVerified(true);
        sendOtp(bodyData.email);
      } else {
        modalNotification({
          type: "error",
          message,
        });
      }
    } catch (error) {
      logger(error);
    }
    setLoading(false);
  };

  const verifyOtp = async (values) => {
    setLoading(true);
    try {
      let bodyData = { ...values };
      const enteredOtp= `${bodyData.input1}${bodyData.input2}${bodyData.input3}${bodyData.input4}${bodyData.input5}${bodyData.input6}`;
      bodyData = {
        request_id: String(requestId),
        code: enteredOtp
      };
      const response = await AdminAuthServices.verifyEmailOTPService(bodyData);
      const { success, message } = response;
      if (success === 1) {
        modalNotification({
          type: "success",
          message,
        });
        dispatch(login(loginData));
        setLocalStorageToken(loginData?.jwt);
        navigate(adminRoutesMap.DASHBOARD.path);
      } else {
        modalNotification({
          type: "error",
          message,
        });
        setLoading(false);
      }
    } catch (error) {
      logger(error);
      setLoading(false);
    }
  };

  const creds=(mail,pwd)=>{
    onSubmit({email: mail, password: pwd});
  }

  return (
    <>
      {userVerified ? 
        <OtpVerificationForm 
          onSubmit={verifyOtp}
          t={t} 
          loading={loading} 
          otp={otp}
          setOtp={setOtp}
          resendCode={sendOtp}
          resetPwd={resetPwd}
        />
        :
        <AdminLoginForm 
          onSubmit={creds} 
          t={t} 
          loading={loading} 
          setResetPwd={setResetPwd}
        />
      }
    </>
  );
}

export default Login;
