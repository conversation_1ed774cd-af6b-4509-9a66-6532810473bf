.status-management-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  @media (max-width: 768px) {
    margin: 1rem;
    padding: 1rem;
  }
}

.page-header {
  margin-bottom: 2rem;

  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }

  .subtitle {
    color: #6b7280;
    font-size: 1rem;
  }

  .divider {
    height: 1px;
    background: linear-gradient(to right, #e5e7eb, transparent);
    margin: 1.5rem 0;
  }
}

.create-section {
  background: #ffffff;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;

  h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:before {
      content: "➕";
      font-size: 1.25rem;
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .form-group {
    .form-label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
    }

    .form-input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 0.875rem;
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    .form-textarea {
      @extend .form-input;
      min-height: 80px;
      resize: vertical;
    }

    .form-checkbox {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-top: 0.5rem;

      input[type="checkbox"] {
        width: 1rem;
        height: 1rem;
        accent-color: #3b82f6;
      }

      label {
        font-size: 0.875rem;
        color: #374151;
        cursor: pointer;
      }
    }

    .color-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .color-picker {
        width: 3rem;
        height: 2.5rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        cursor: pointer;
        background: white;
        padding: 0;

        &::-webkit-color-swatch-wrapper {
          padding: 0;
        }

        &::-webkit-color-swatch {
          border: none;
          border-radius: 6px;
        }
      }

      .color-text {
        flex: 1;
      }
    }
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;

    @media (max-width: 768px) {
      .btn {
        flex: 1;
      }
    }
  }
}

.statuses-section {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    padding: 1.5rem 2rem;
    margin: 0;
    border-bottom: 1px solid #e5e7eb;
    background: #f8fafc;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:before {
      content: "📋";
      font-size: 1.25rem;
    }
  }
}

.statuses-table-container {
  overflow-x: auto;
}

.statuses-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;

  thead {
    tr {
      background: #f1f3f4;
    }

    th {
      padding: 1rem;
      text-align: left;
      font-weight: 600;
      font-size: 0.875rem;
      color: #374151;
      border-bottom: 2px solid #e5e7eb;
      white-space: nowrap;
    }
  }

  tbody {
    tr {
      border-bottom: 1px solid #f3f4f6;
      transition: background-color 0.2s ease;

      &:hover {
        background: #f8fafc;
      }
    }

    td {
      padding: 1rem;
      font-size: 0.875rem;
      color: #374151;
      vertical-align: middle;
    }
  }
}

.status-color-display {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  .color-circle {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    border: 2px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .color-code {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.75rem;
    color: #6b7280;
    background: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
  }
}

.final-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;

  &.is-final {
    background: #dcfce7;
    color: #166534;

    &:before {
      content: "✅";
      margin-right: 0.25rem;
    }
  }

  &.not-final {
    background: #fef3c7;
    color: #92400e;

    &:before {
      content: "⏳";
      margin-right: 0.25rem;
    }
  }
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  text-decoration: none;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow: none;
  }

  &.btn-primary {
    background: #3b82f6;
    color: white;

    &:hover:not(:disabled) {
      background: #2563eb;
    }
  }

  &.btn-success {
    background: #10b981;
    color: white;

    &:hover:not(:disabled) {
      background: #059669;
    }
  }

  &.btn-danger {
    background: #ef4444;
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;

    &:hover:not(:disabled) {
      background: #dc2626;
    }
  }

  &.btn-secondary {
    background: #6b7280;
    color: white;

    &:hover:not(:disabled) {
      background: #4b5563;
    }
  }
}

.success-message {
  padding: 1rem 1.5rem;
  background: #ecfdf5;
  border: 1px solid #bbf7d0;
  color: #166534;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: slideIn 0.3s ease-out;

  &:before {
    content: "✅";
    font-size: 1rem;
  }
}

.error-message {
  padding: 1rem 1.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: slideIn 0.3s ease-out;

  &:before {
    content: "❌";
    font-size: 1rem;
  }
}

.loading-container {
  text-align: center;
  padding: 4rem 2rem;

  .loading-text {
    font-size: 1.125rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }

  .spinner {
    margin: 2rem auto;
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.no-data {
  text-align: center;
  padding: 3rem 2rem;
  color: #6b7280;

  .no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .no-data-text {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .no-data-subtext {
    font-size: 0.875rem;
  }
}

.description-cell {
  max-width: 250px;
  word-wrap: break-word;
  line-height: 1.4;

  .description-text {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .no-description {
    font-style: italic;
    color: #9ca3af;
  }
}

.date-cell {
  white-space: nowrap;
  font-size: 0.75rem;
  color: #6b7280;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 968px) {
  .statuses-table {
    font-size: 0.75rem;

    th, td {
      padding: 0.75rem 0.5rem;
    }
  }

  .description-cell {
    max-width: 150px;
  }
}