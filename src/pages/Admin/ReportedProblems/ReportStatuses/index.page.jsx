import React, { useState, useEffect } from 'react';
import { AdminReportStatusesServices } from '../../../../services/Admin/ReportedProblems/ReportStatuses/index.service';
import './ReportStatuses.scss';

function ReportStatuses() {
  const [statuses, setStatuses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [createLoading, setCreateLoading] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    color_code: '#3b82f6',
    is_final: false,
    description: ''
  });

  const fetchStatuses = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await AdminReportStatusesServices.getAllStatuses();
      
      if (response?.success === 1 && response.data?.statuses) {
        setStatuses(response.data.statuses);
      } else {
        setError('Failed to load statuses: Invalid response structure');
      }
    } catch (err) {
      setError(`Failed to load statuses: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch statuses on component mount
  useEffect(() => {
    fetchStatuses();
  }, []);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (successMessage || errorMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
        setErrorMessage(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage, errorMessage]);

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateStatus = async (e) => {
    e.preventDefault();
    console.log("status Name:", !formData.name.trim());
    // if (!formData.name.trim()) {
    //   setErrorMessage('Status name is required');
    //   return;
    // }

    try {
      setCreateLoading(true);
      setErrorMessage(null);
      setSuccessMessage(null);
        console.log("Creating status with data:", formData.name.trim());
      const response = await AdminReportStatusesServices.createStatus({
        name: formData.name.trim(),
        color_code: formData.color_code,
        is_final: formData.is_final ? 1 : 0,
        description: formData.description.trim() || null
      });
      console.log(response, "API Response:");
      if (response?.success === 1) {
        setSuccessMessage('Status created successfully!');
        
        // Reset form
        setFormData({
          name: '',
          color_code: '#3b82f6',
          is_final: false,
          description: ''
        });

        // Refresh statuses list
        await fetchStatuses();
      } else {
        setErrorMessage(response?.message || 'Failed to create status');
      }
    } catch (err) {
      setErrorMessage(err.message);
    } finally {
      setCreateLoading(false);
    }
  };

  const handleDeleteStatus = async (statusId, statusName) => {
    if (!window.confirm(`Are you sure you want to delete the status "${statusName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setErrorMessage(null);
      setSuccessMessage(null);

      const response = await AdminReportStatusesServices.deleteStatus(statusId);

      if (response?.success === 1) {
        setSuccessMessage('Status deleted successfully!');
        
        // Refresh statuses list
        await fetchStatuses();
      } else {
        setErrorMessage(response?.message || 'Failed to delete status');
      }
    } catch (err) {
      setErrorMessage(err.message);
    }
  };

  if (loading) {
    return (
      <div className="status-management-container">
        <div className="loading-container">
          <div className="spinner"/>
          <p className="loading-text">Loading statuses...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="status-management-container">
      {/* Page Header */}
      <div className="page-header">
        <h1>📊 Report Status Management</h1>
        <p className="subtitle">
          Manage status options for reports. Create, view, and delete status configurations.
        </p>
        <div className="divider"/>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="success-message">
          {successMessage}
        </div>
      )}
      {errorMessage && (
        <div className="error-message">
          {errorMessage}
        </div>
      )}
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Create Status Section */}
      <div className="create-section">
        <h2>Create New Status</h2>
        <form onSubmit={handleCreateStatus}>
          <div className="form-grid">
            <div className="form-group">
              <label className="form-label">Status Name *</label>
              <input
                type="text"
                className="form-input"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                placeholder="Enter status name (e.g., Open, In Progress, Closed)"
                required
                maxLength={100}
              />
            </div>

            <div className="form-group">
              <label className="form-label">Color</label>
              <div className="color-input-wrapper">
                <input
                  type="color"
                  className="color-picker"
                  value={formData.color_code}
                  onChange={(e) => handleFormChange('color_code', e.target.value)}
                />
                <input
                  type="text"
                  className="form-input color-text"
                  value={formData.color_code}
                  onChange={(e) => handleFormChange('color_code', e.target.value)}
                  placeholder="#3b82f6"
                  pattern="^#[0-9A-Fa-f]{6}$"
                />
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">Status Type</label>
              <div className="form-checkbox">
                <input
                  type="checkbox"
                  id="is_final"
                  checked={formData.is_final}
                  onChange={(e) => handleFormChange('is_final', e.target.checked)}
                />
                <label htmlFor="is_final">
                  This is a final status (reports with this status are considered resolved)
                </label>
              </div>
            </div>

            <div className="form-group" style={{ gridColumn: '1 / -1' }}>
              <label className="form-label">Description (Optional)</label>
              <textarea
                className="form-textarea"
                value={formData.description}
                onChange={(e) => handleFormChange('description', e.target.value)}
                placeholder="Enter a description for this status..."
                maxLength={500}
                rows={3}
              />
            </div>
          </div>

          <div className="form-actions">
            <button 
              type="submit" 
              className="btn btn-success"
              disabled={createLoading || !formData.name.trim()}
            >
              {createLoading ? '⏳ Creating...' : 'Create'}
            </button>
            <button 
              type="button" 
              className="btn btn-secondary"
              onClick={() => setFormData({
                name: '',
                color_code: '#3b82f6',
                is_final: false,
                description: ''
              })}
              disabled={createLoading}
            >
              Reset
            </button>
          </div>
        </form>
      </div>

      {/* Statuses List Section */}
      <div className="statuses-section">
        <h2>Existing Statuses ({statuses.length})</h2>
        
        {statuses.length === 0 ? (
          <div className="no-data">
            <div className="no-data-icon">📋</div>
            <p className="no-data-text">No statuses found</p>
            <p className="no-data-subtext">Create your first status using the form above.</p>
          </div>
        ) : (
          <div className="statuses-table-container">
            <table className="statuses-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Color</th>
                  <th>Type</th>
                  <th>Description</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {statuses.map(status => (
                  <tr key={status.id}>
                    <td>#{status.id}</td>
                    <td>
                      <strong>{status.name}</strong>
                    </td>
                    <td>
                      <div className="status-color-display">
                        <div 
                          className="color-circle"
                          style={{ backgroundColor: status.color_code }}
                        />
                        <span className="color-code">{status.color_code}</span>
                      </div>
                    </td>
                    <td>
                      <span className={`final-status-badge ${status.is_final ? 'is-final' : 'not-final'}`}>
                        {status.is_final ? 'Final' : 'Ongoing'}
                      </span>
                    </td>
                    <td>
                      <div className="description-cell">
                        {status.description ? (
                          <div className="description-text">{status.description}</div>
                        ) : (
                          <span className="no-description">No description</span>
                        )}
                      </div>
                    </td>
                    <td className="date-cell">
                      {new Date(status.created_at).toLocaleDateString()}
                      <br />
                      {new Date(status.created_at).toLocaleTimeString()}
                    </td>
                    <td>
                      <button
                        onClick={() => handleDeleteStatus(status.id, status.name)}
                        className="btn btn-danger"
                        title={`Delete ${status.name}`}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

export default ReportStatuses;