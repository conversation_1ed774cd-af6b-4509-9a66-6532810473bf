.category-management-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  @media (max-width: 768px) {
    margin: 1rem;
    padding: 1rem;
  }
}

.page-header {
  margin-bottom: 2rem;

  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }

  .subtitle {
    color: #6b7280;
    font-size: 1rem;
  }

  .divider {
    height: 1px;
    background: linear-gradient(to right, #e5e7eb, transparent);
    margin: 1.5rem 0;
  }
}

.create-section {
  background: #ffffff;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:before {
      content: "➕";
      font-size: 1.25rem;
    }
  }

  .form-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    .form-label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
    }

    .form-input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 0.875rem;
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      &:disabled {
        background-color: #f9fafb;
        cursor: not-allowed;
      }
    }
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;

    @media (max-width: 768px) {
      .btn {
        flex: 1;
      }
    }
  }
}

.categories-list-section {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    padding: 1.5rem 2rem;
    margin: 0;
    border-bottom: 1px solid #e5e7eb;
    background: #f8fafc;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:before {
      content: "📂";
      font-size: 1.25rem;
    }
  }

  .no-categories-message {
    text-align: center;
    padding: 3rem 2rem;
    color: #6b7280;
    font-size: 1rem;
    background: #f9fafb;
    margin: 0;
  }
}

.categories-table-container {
  overflow-x: auto;
}

.categories-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px;

  thead {
    tr {
      background: #f1f3f4;
    }

    th {
      padding: 1rem;
      text-align: left;
      font-weight: 600;
      font-size: 0.875rem;
      color: #374151;
      border-bottom: 2px solid #e5e7eb;
      white-space: nowrap;
    }
  }

  tbody {
    tr {
      border-bottom: 1px solid #f3f4f6;
      transition: background-color 0.2s ease;

      &:hover {
        background: #f8fafc;
      }
    }

    td {
      padding: 1rem;
      font-size: 0.875rem;
      color: #374151;
      vertical-align: middle;
    }
  }
}

.category-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.95rem;
}

.date-cell {
  white-space: nowrap;
  font-size: 0.75rem;
  color: #6b7280;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  text-decoration: none;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow: none;
  }

  &.btn-primary {
    background: #3b82f6;
    color: white;

    &:hover:not(:disabled) {
      background: #2563eb;
    }
  }

  &.btn-success {
    background: #10b981;
    color: white;

    &:hover:not(:disabled) {
      background: #059669;
    }
  }

  &.btn-danger {
    background: #ef4444;
    color: white;
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;

    &:hover:not(:disabled) {
      background: #dc2626;
    }
  }

  &.btn-secondary {
    background: #6b7280;
    color: white;

    &:hover:not(:disabled) {
      background: #4b5563;
    }
  }
}

.success-message {
  padding: 1rem 1.5rem;
  background: #ecfdf5;
  border: 1px solid #bbf7d0;
  color: #166534;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: slideIn 0.3s ease-out;

  &:before {
    content: "✅";
    font-size: 1rem;
  }
}

.error-message {
  padding: 1rem 1.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: slideIn 0.3s ease-out;

  &:before {
    content: "❌";
    font-size: 1rem;
  }
}

.loading-container {
  text-align: center;
  padding: 4rem 2rem;

  .loading-text {
    font-size: 1.125rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }

  .spinner {
    margin: 2rem auto;
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.no-data {
  text-align: center;
  padding: 3rem 2rem;
  color: #6b7280;

  .no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .no-data-text {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .no-data-subtext {
    font-size: 0.875rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
  }

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 968px) {
  .categories-table {
    font-size: 0.75rem;

    th, td {
      padding: 0.75rem 0.5rem;
    }
  }
}