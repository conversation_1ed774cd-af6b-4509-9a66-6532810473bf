import React, { useState, useEffect } from 'react';
import { AdminReportIssueCategoriesServices } from '../../../../services/Admin/ReportedProblems/ReportedIssueCategories/index.service';
import './ReportedIssueCategories.scss';

function ReportedIssueCategories() {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [createLoading, setCreateLoading] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: ''
  });

    const fetchCategories = async () => {
    try {
        setLoading(true);
        setError(null);
        
        const response = await AdminReportIssueCategoriesServices.getAllCategories();
        
        if (response?.success === 1 && response.data?.categories) {
        setCategories(response.data.categories);
        } else {
        setError('Failed to load categories: Invalid response structure');
        }
    } catch (err) {
        setError(`Failed to load categories: ${err.message}`);
    } finally {
        setLoading(false);
    }
    };

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (successMessage || errorMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
        setErrorMessage(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage, errorMessage]);


  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateCategory = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setErrorMessage('Category name is required');
      return;
    }

    try {
      setCreateLoading(true);
      setErrorMessage(null);
      setSuccessMessage(null);

      const response = await AdminReportIssueCategoriesServices.createCategory({
        name: formData.name.trim()
      });

      if (response?.success === 1) {
        setSuccessMessage('Issue category created successfully!');
        
        // Reset form
        setFormData({
          name: ''
        });

        // Refresh categories list
        await fetchCategories();
      } else {
        setErrorMessage(response?.message || 'Failed to create category');
      }
    } catch (err) {
      setErrorMessage(err.message);
    } finally {
      setCreateLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId, categoryName) => {
    if (!window.confirm(`Are you sure you want to delete the category "${categoryName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setErrorMessage(null);
      setSuccessMessage(null);

      const response = await AdminReportIssueCategoriesServices.deleteCategory(categoryId);

      if (response?.success === 1) {
        setSuccessMessage('Issue category deleted successfully!');
        
        // Refresh categories list
        await fetchCategories();
      } else {
        setErrorMessage(response?.message || 'Failed to delete category');
      }
    } catch (err) {
      setErrorMessage(err.message);
    }
  };

  if (loading) {
    return (
      <div className="category-management-container">
        <div className="loading-container">
          <div className="spinner"/>
          <p className="loading-text">Loading categories...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="category-management-container">
      {/* Page Header */}
      <div className="page-header">
        <h1>📂 Issue Categories Management</h1>
        <p className="subtitle">
          Manage issue categories for reports. Create, view, and delete category options.
        </p>
        <div className="divider"/>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="success-message">
          {successMessage}
        </div>
      )}
      {errorMessage && (
        <div className="error-message">
          {errorMessage}
        </div>
      )}
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Create Category Section */}
      <div className="create-section">
        <h2>Create New Issue Category</h2>
        <form onSubmit={handleCreateCategory}>
          <div className="form-grid">
            <div className="form-group">
              <label className="form-label">Category Name *</label>
              <input
                type="text"
                className="form-input"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                placeholder="Enter category name (e.g., Audio Issue, Video Issue, Connection Problem)"
                required
                maxLength={100}
                disabled={createLoading}
              />
            </div>
            
            <div className="form-actions">
              <button 
                type="submit" 
                className="btn btn-success"
                disabled={createLoading || !formData.name.trim()}
              >
                {createLoading ? '⏳ Creating...' : 'Create'}
              </button>
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={() => setFormData({ name: '' })}
                disabled={createLoading}
              >
                Reset
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Categories List Section */}
      <div className="categories-list-section">
        <h2>Existing Categories ({categories.length})</h2>
        
        {categories.length === 0 ? (
          <div className="no-data">
            <div className="no-data-icon">📂</div>
            <p className="no-data-text">No categories found</p>
            <p className="no-data-subtext">Create your first issue category using the form above.</p>
          </div>
        ) : (
          <div className="categories-table-container">
            <table className="categories-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Category Name</th>
                  <th>Created</th>
                  <th>Last Updated</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {categories.map(category => (
                  <tr key={category.id}>
                    <td>#{category.id}</td>
                    <td>
                      <span className="category-name">{category.name}</span>
                    </td>
                    <td className="date-cell">
                      {new Date(category.created_at).toLocaleDateString()}
                      <br />
                      {new Date(category.created_at).toLocaleTimeString()}
                    </td>
                    <td className="date-cell">
                      {new Date(category.updated_at).toLocaleDateString()}
                      <br />
                      {new Date(category.updated_at).toLocaleTimeString()}
                    </td>
                    <td>
                      <button
                        onClick={() => handleDeleteCategory(category.id, category.name)}
                        className="btn btn-danger"
                        title={`Delete ${category.name}`}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

export default ReportedIssueCategories;