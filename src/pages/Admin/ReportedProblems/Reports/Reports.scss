.reports-container {
  padding: 20px;
  font-family: Arial, sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.reports-header {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;

  h1 {
    margin: 0;
    color: #333;
  }
}

.stats-section {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.stat-card {
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 100px;

  h3 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: bold;
  }

  p {
    margin: 0;
    font-size: 12px;
    color: #666;
  }
}

.filters-section {
  background-color: #ffffffd3;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-section {
  margin-bottom: 15px;

  form {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
}

.filter-row {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 10px;
}

.date-filter-row {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  strong {
    margin-right: 10px;
  }

  .date-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;

    label {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.input-field {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
}

.date-input {
  @extend .input-field;
  min-width: 150px;
}

.select-field {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 150px;
  background-color: white;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.btn-attachments {
  @extend .btn;
  background-color: #007bff;
  color: white;
  padding: 6px 8px;
  font-size: 12px;
  padding: 6px 8px;
}

.btn-primary {
  @extend .btn;
  background-color: #007bff;
  color: white;

  &:hover:not(:disabled) {
    background-color: #0056b3;
  }
}

.btn-secondary {
  @extend .btn;
  background-color: #6c757d;
  color: white;

  &:hover:not(:disabled) {
    background-color: #545b62;
  }
}

.btn-success {
  @extend .btn;
  background-color: #28a745;
  color: white;
  padding: 6px 12px;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover:not(:disabled) {
    background-color: #218838;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  &:active {
    background-color: #1e7e34;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: scale(0.98);
  }
}

.table-container {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.reports-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1200px;

  thead {
    tr {
      background-color: #f1f3f4;
    }

    th {
      padding: 12px 8px;
      text-align: left;
      font-weight: 600;
      font-size: 13px;
      border-bottom: 2px solid #dee2e6;
      color: #333;
    }
  }

  tbody {
    tr {
      border-bottom: 1px solid #eee;
      
      &:hover {
        background-color: #f8f9fa;
      }
    }

    td {
      padding: 10px 8px;
      font-size: 13px;
      vertical-align: top;
    }
  }
}

.meeting-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;

  strong {
    font-weight: 600;
  }

  small {
    color: #666;
    font-size: 11px;
  }
}

.reporter-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;

  strong {
    font-weight: 600;
  }

  small {
    color: #666;
    font-size: 11px;
  }
}

.description-cell {
  max-width: 200px;
  word-wrap: break-word;
}

.vc-server-cell {
  font-size: 12px;
  word-wrap: break-word;
  max-width: 120px;
}

.type-tag {
  background-color: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  text-transform: capitalize;
  color: #495057;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: #fff;
}

.spam-tag {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  color: #fff;
  font-weight: 500;

  &.spam {
    background-color: #dc3545;
  }

  &.not-spam {
    background-color: #28a745;
  }
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  font-style: italic;
}

.pagination-section {
  text-align: center;
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .pagination-info {
    margin-bottom: 10px;
    color: #666;
    font-size: 14px;
  }

  .pagination-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;

    .page-info {
      padding: 8px 16px;
      color: #333;
      font-weight: 500;
    }
  }
}

.loading-container {
  padding: 40px;
  text-align: center;
  background: #f8f9fa;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .loading-text {
    font-size: 16px;
    color: #666;
  }
}

.error-container {
  padding: 40px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  margin: 20px;
  color: #721c24;

  h3 {
    margin-top: 0;
    margin-bottom: 10px;
  }

  p {
    margin-bottom: 15px;
  }

  details {
    margin-top: 10px;

    summary {
      cursor: pointer;
      font-weight: 500;
    }

    pre {
      font-size: 12px;
      background: #f8f9fa;
      padding: 10px;
      margin-top: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  }

  .retry-btn {
    @extend .btn-primary;
    margin-top: 10px;
  }
}

.date-cell {
  small {
    display: block;
    color: #666;
    font-size: 11px;
  }
}