import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  MdFileDownload,
  MdOutlineZoomIn,
  MdOutlineZoomOut,
  MdZoomInMap,
  MdChevronLeft,
  MdChevronRight,
  MdClose,
} from 'react-icons/md';
import './AttachmentsViewer.scss';

const isImageFile = (name = '') =>
  /\.(jpeg|jpg|png|gif|webp)$/i.test(name);
const isVideoFile = (name = '') =>
  /\.(mp4|avi|mov|wmv|flv|webm)$/i.test(name);

function AttachmentsViewer({
  isOpen,
  onClose,
  attachments = [],
  initialIndex = 0,
}) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });

  const resetView = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  useEffect(() => {
    if (isOpen) {
      setCurrentIndex(initialIndex);
      resetView();
    }
  }, [isOpen, initialIndex]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const goToPrevious = () => {
    setCurrentIndex((prev) =>
      prev > 0 ? prev - 1 : attachments.length - 1
    );
    resetView();
  };

  const goToNext = () => {
    setCurrentIndex((prev) =>
      prev < attachments.length - 1 ? prev + 1 : 0
    );
    resetView();
  };

  const zoomIn = () => setScale((prev) => Math.min(prev + 0.2, 3));
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.2, 1));

  const handleMouseDown = (e) => {
    e.stopPropagation();
    setIsDragging(true);
    setStartPos({ x: e.clientX - position.x, y: e.clientY - position.y });
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - startPos.x,
        y: e.clientY - startPos.y,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleDownload = () => {
    const currentAttachment = attachments[currentIndex];
    if (currentAttachment?.file_url) {
      window.open(currentAttachment.file_url, '_blank');
    }
  };

  const handleClose = () => {
    setPosition({ x: 0, y: 0 });
    setScale(1);
    setIsDragging(false);
    setCurrentIndex(initialIndex);
    onClose();
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const handleKeyDown = (e) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'Escape':
        handleClose();
        break;
      case 'ArrowLeft':
        goToPrevious();
        break;
      case 'ArrowRight':
        goToNext();
        break;
      case '+':
      case '=':
        zoomIn();
        break;
      case '-':
        zoomOut();
        break;
      case '0':
        resetView();
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, currentIndex, scale]);

  if (!isOpen || attachments.length === 0) return null;

  const currentAttachment = attachments[currentIndex];
  const fileUrl =
    currentAttachment?.file_url ||
    currentAttachment?.url ||
    '';
  const fileName =
    currentAttachment?.filename ||
    currentAttachment?.name ||
    'Unknown file';
  const isImage = isImageFile(fileName);
  const isVideo = isVideoFile(fileName);

  return (
    <div className="attachments-viewer-overlay" onClick={handleBackdropClick}>
      <div className="attachments-viewer-container">
        <div className="attachments-viewer-header">
          <div className="file-info">
            <span className="file-name">{fileName}</span>
            <span className="file-counter">
              {currentIndex + 1} of {attachments.length}
            </span>
          </div>
          <div className="header-controls">
            <button
              type="button"
              onClick={handleDownload}
              className="control-button download-button"
              title="Download file"
              aria-label="Download file"
            >
              <MdFileDownload />
            </button>
            {isImage && (
              <>
                <button
                  type="button"
                  onClick={zoomIn}
                  className="control-button zoom-button"
                  title="Zoom in"
                  aria-label="Zoom in"
                >
                  <MdOutlineZoomIn />
                </button>
                <button
                  type="button"
                  onClick={zoomOut}
                  className="control-button zoom-button"
                  title="Zoom out"
                  aria-label="Zoom out"
                >
                  <MdOutlineZoomOut />
                </button>
                <button
                  type="button"
                  onClick={resetView}
                  className="control-button zoom-button"
                  title="Reset view"
                  aria-label="Reset view"
                >
                  <MdZoomInMap />
                </button>
              </>
            )}
            <button
              type="button"
              onClick={handleClose}
              className="control-button close-button"
              title="Close viewer"
              aria-label="Close viewer"
            >
              <MdClose />
            </button>
          </div>
        </div>

        <div className="attachments-viewer-content">
          {isImage ? (
            <div
              className="image-container"
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            >
              <img
                src={fileUrl}
                alt={fileName}
                className="preview-image"
                style={{
                  transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
                  transition: isDragging
                    ? 'none'
                    : 'transform 0.2s ease-in-out',
                  cursor: isDragging ? 'grabbing' : 'grab',
                }}
                onMouseDown={handleMouseDown}
                draggable={false}
              />
            </div>
          ) : isVideo ? (
            <div className="video-container">
              <video
                controls
                src={fileUrl}
                className="preview-video"
                aria-label={`Video: ${fileName}`}
              >
                <track kind="captions" src="" label="English" />
                Your browser does not support the video tag.
              </video>
            </div>
          ) : (
            <div className="file-preview">
              <div className="file-icon" aria-label="No preview available">
                📄
              </div>
              <p className="file-message">
                Preview not available for this file type.
              </p>
              <button
                type="button"
                onClick={handleDownload}
                className="download-button"
                aria-label="Download file"
              >
                Download File
              </button>
            </div>
          )}
        </div>

        {attachments.length > 1 && (
          <>
            <button
              type="button"
              className="nav-arrow nav-arrow-left"
              onClick={goToPrevious}
              title="Previous attachment"
              aria-label="Previous attachment"
            >
              <MdChevronLeft />
            </button>
            <button
              type="button"
              className="nav-arrow nav-arrow-right"
              onClick={goToNext}
              title="Next attachment"
              aria-label="Next attachment"
            >
              <MdChevronRight />
            </button>
            <div className="navigation-dots">
              {attachments.map((_, index) => (
                <button
                  key={index}
                  type="button"
                  className={`dot ${index === currentIndex ? 'active' : ''}`}
                  onClick={() => {
                    setCurrentIndex(index);
                    resetView();
                  }}
                  title={`Go to attachment ${index + 1}`}
                  aria-label={`Go to attachment ${index + 1}`}
                />
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}

AttachmentsViewer.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  attachments: PropTypes.arrayOf(
    PropTypes.shape({
      file_url: PropTypes.string,
      url: PropTypes.string,
      filename: PropTypes.string,
      name: PropTypes.string,
    })
  ),
  initialIndex: PropTypes.number,
};

AttachmentsViewer.defaultProps = {
  attachments: [],
  initialIndex: 0,
};

export default AttachmentsViewer; 