.attachments-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.attachments-viewer-container {
  width: 100%;
  height: 100%;
  max-width: 1400px;
  max-height: 90vh;
  background: #1a1a1a;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  position: relative;
}

.attachments-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  border-bottom: 1px solid #404040;
  flex-shrink: 0;
  min-height: 80px;

  .file-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    flex: 1;

    .file-name {
      font-size: 18px;
      font-weight: 600;
      max-width: 500px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .file-counter {
      font-size: 14px;
      opacity: 0.8;
    }
  }

  .header-controls {
    display: flex;
    gap: 12px;
    align-items: center;

    .control-button {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      color: white;
      cursor: pointer;
      padding: 10px;
      border-radius: 6px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 44px;
      height: 44px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
      }

      svg {
        width: 22px;
        height: 22px;
      }

      &.download-button:hover {
        color: #4ade80;
        background-color: rgba(74, 222, 128, 0.2);
      }

      &.zoom-button:hover {
        color: #60a5fa;
        background-color: rgba(96, 165, 250, 0.2);
      }

      &.close-button:hover {
        color: #ef4444;
        background-color: rgba(239, 68, 68, 0.2);
      }
    }
  }
}

.attachments-viewer-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  min-height: 400px;
  position: relative;
  background: #0f0f0f;

  .image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 8px;
    background-color: #f5f5f5;

    .preview-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border-radius: 8px;
      user-select: none;
    }
  }

  .video-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .preview-video {
      max-width: 100%;
      max-height: 70vh;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .file-preview {
    text-align: center;
    padding: 40px;
    color: #666;

    .file-icon {
      font-size: 64px;
      margin-bottom: 20px;
    }

    .file-message {
      margin-bottom: 24px;
      font-size: 18px;
      color: #999;
    }

    .download-button {
      background: #3b82f6;
      color: white;
      border: none;
      padding: 14px 28px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        background: #2563eb;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      }
    }
  }
}

.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  border: none;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 100;
  backdrop-filter: blur(4px);

  &:hover {
    background: rgba(0, 0, 0, 0.95);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  }

  svg {
    width: 28px;
    height: 28px;
  }

  &.nav-arrow-left {
    left: 30px;
  }

  &.nav-arrow-right {
    right: 30px;
  }
}

.navigation-dots {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 100;
  padding: 10px 20px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  backdrop-filter: blur(4px);

  .dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.6);
      transform: scale(1.2);
    }

    &.active {
      background: white;
      transform: scale(1.3);
      box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
    }
  }
}

// Keyboard shortcuts hint
.attachments-viewer-overlay::before {
  content: "Press ESC to close, Arrow keys to navigate, +/- to zoom";
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10000;
}

.attachments-viewer-overlay:hover::before {
  opacity: 1;
}

// Responsive design
@media (max-width: 768px) {
  .attachments-viewer-overlay {
    padding: 10px;
  }

  .attachments-viewer-container {
    max-height: 95vh;
  }

  .attachments-viewer-header {
    padding: 15px;
    min-height: 70px;

    .file-info {
      .file-name {
        max-width: 250px;
        font-size: 16px;
      }
    }

    .header-controls {
      gap: 8px;

      .control-button {
        min-width: 40px;
        height: 40px;

        svg {
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  .attachments-viewer-content {
    padding: 15px;
    min-height: 300px;
  }

  .nav-arrow {
    width: 50px;
    height: 50px;

    svg {
      width: 24px;
      height: 24px;
    }

    &.nav-arrow-left {
      left: 15px;
    }

    &.nav-arrow-right {
      right: 15px;
    }
  }

  .navigation-dots {
    bottom: 20px;
    padding: 8px 16px;

    .dot {
      width: 12px;
      height: 12px;
    }
  }
}

@media (max-width: 480px) {
  .attachments-viewer-header {
    .file-info {
      .file-name {
        max-width: 200px;
        font-size: 14px;
      }
    }

    .header-controls {
      gap: 6px;

      .control-button {
        min-width: 36px;
        height: 36px;

        svg {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}
