import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from "react-router-dom";
import { AdminReportsServices } from '../../../../services/Admin/ReportedProblems/Reports/index.service';
import './Reports.scss';
import AttachmentsViewer from './components/AttachmentsViewer';


function Reports() {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [reports, setReports] = useState([]);
  const [filters, setFilters] = useState({});
  const [pagination, setPagination] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  
  // Initialize state from URL params
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [selectedFilters, setSelectedFilters] = useState({
    status_id: searchParams.get('status_id') || '',
    issue_category_id: searchParams.get('issue_category_id') || '',
    reporter_type: searchParams.get('reporter_type') || '',
    platform_name: searchParams.get('platform_name') || '',
    vc_server_host: searchParams.get('vc_server_host') || '',
    is_spam: searchParams.get('is_spam') || '',
    date_from: searchParams.get('date_from') || '',
    date_to: searchParams.get('date_to') || ''
  });

  // Separate state for date filters (not immediately applied)
  const [tempDateFilters, setTempDateFilters] = useState({
    date_from: searchParams.get('date_from') || '',
    date_to: searchParams.get('date_to') || ''
  });

  const [attachmentsModalOpen, setAttachmentsModalOpen] = useState(false);
  const [attachmentsModalFiles, setAttachmentsModalFiles] = useState([]);
  const [attachmentsModalIndex, setAttachmentsModalIndex] = useState(0);

  const parseDescriptionWithoutLocation = (description) => {
    if (!description) return '';
    
    const cleanedDescription = description.replace(/\n\n\[LOCATION: .+?\]$/, '');
    return cleanedDescription.trim();
  };

  // Function to get unique VC server hosts from current reports
  const getUniqueVcServerHosts = () => {
    if (!reports || reports.length === 0) return [];
    
    const uniqueHosts = [...new Set(
      reports
        .map(report => report.vc_server_host)
        .filter(host => host && host.trim() !== '' && host !== 'N/A')
    )].sort();
    
    return uniqueHosts;
  };

  // Function to format date for input[type="date"]
  // const formatDateForInput = (dateString) => {
  //   if (!dateString) return '';
  //   const date = new Date(dateString);
  //   return date.toISOString().split('T')[0];
  // };

  // Function to update URL parameters
  const updateUrlParams = (params) => {
    const newSearchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        newSearchParams.set(key, value);
      }
    });
    
    setSearchParams(newSearchParams);
  };

  // Function to sync selectedFilters with URL params
  const syncFiltersWithUrl = () => {
    const urlFilters = {
      status_id: searchParams.get('status_id') || '',
      issue_category_id: searchParams.get('issue_category_id') || '',
      reporter_type: searchParams.get('reporter_type') || '',
      platform_name: searchParams.get('platform_name') || '',
      vc_server_host: searchParams.get('vc_server_host') || '',
      is_spam: searchParams.get('is_spam') || '',
      date_from: searchParams.get('date_from') || '',
      date_to: searchParams.get('date_to') || ''
    };
    
    setSelectedFilters(urlFilters);
    setTempDateFilters({
      date_from: urlFilters.date_from,
      date_to: urlFilters.date_to
    });
    setSearchTerm(searchParams.get('search') || '');
  };

  const fetchReports = async (params = {}) => {
    try {
      setLoading(true);
      setError(null);
      
      // Clean up params - remove empty values
      const cleanParams = Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value;
        }
        return acc;
      }, {});
      
      // Pass the parameters to the API call
      const response = await AdminReportsServices.getAllReports(cleanParams);
      console.log("Report: ",response);
      // Check different possible response structures
      if (response && response.data && response.data.success === 1) {
        const apiData = response.data.data;
        
        setReports(apiData.reports || []);
        
        setFilters(apiData.filters || {});
        setPagination(apiData.pagination || {});
      } else if (response && response.success === 1) {
        // Alternative response structure
        const apiData = response.data;
        
        setReports(apiData.reports || []);
        setFilters(apiData.filters || {});
        setPagination(apiData.pagination || {});
      } else {
        setError('Invalid API response structure');
      }
    } catch (err) {
      setError(err?.message || "Failed to load reports");
    } finally {
      setLoading(false);
    }
  };
  // Load data on component mount ONLY
  useEffect(() => {
    const initialParams = {};
    
    // Get params from URL on initial load
    for (const [key, value] of searchParams.entries()) {
      initialParams[key] = value;
    }
    
    fetchReports(initialParams);
    setIsInitialLoad(false);
  }, []); // Only run on mount

  // Separate effect to handle URL parameter changes (after initial load)
  useEffect(() => {
    if (isInitialLoad) {
      return;
    }

    // Sync filters with URL first
    syncFiltersWithUrl();

    const urlParams = {};
    for (const [key, value] of searchParams.entries()) {
      urlParams[key] = value;
    }
    
    // ALWAYS fetch when URL changes, even if no parameters (this handles "All Reports" case)
    fetchReports(urlParams);
  }, [searchParams.toString(), isInitialLoad]);

  const handleSearch = (e) => {
    e.preventDefault();
    
    // Build search parameters
    const searchParamsObj = {};
    
    // Add search term if provided
    if (searchTerm.trim()) {
      searchParamsObj.search = searchTerm.trim();
    }
    
    // Add active filters
    Object.entries(selectedFilters).forEach(([key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        searchParamsObj[key] = value;
      }
    });
    
    // Update URL first, then the useEffect will trigger fetchReports
    updateUrlParams(searchParamsObj);
  };

  const handleFilterChange = (filterKey, value) => {
    // Create new filters with the change
    const newFilters = { ...selectedFilters, [filterKey]: value };
    
    // Build filter parameters for URL
    const filterParams = {};
    
    // Add search term if provided
    if (searchTerm.trim()) {
      filterParams.search = searchTerm.trim();
    }
    
    // Add all filters (including the new one) - only non-empty values go to URL
    Object.entries(newFilters).forEach(([key, val]) => {
      if (val !== '' && val !== null && val !== undefined) {
        filterParams[key] = val;
      }
    });
    
    // Update URL first - the useEffect will then sync the state and call fetchReports
    updateUrlParams(filterParams);
  };

  // New function to handle date filter changes (without immediate API call)
  const handleDateFilterChange = (filterKey, value) => {
    setTempDateFilters(prev => ({
      ...prev,
      [filterKey]: value
    }));
  };

  // New function to apply date filters
  const applyDateFilters = () => {
    // Create new filters with the applied date filters
    const newFilters = { 
      ...selectedFilters, 
      date_from: tempDateFilters.date_from,
      date_to: tempDateFilters.date_to
    };
    
    // Build filter parameters for URL
    const filterParams = {};
    
    // Add search term if provided
    if (searchTerm.trim()) {
      filterParams.search = searchTerm.trim();
    }
    
    // Add all filters (including the applied date filters) - only non-empty values go to URL
    Object.entries(newFilters).forEach(([key, val]) => {
      if (val !== '' && val !== null && val !== undefined) {
        filterParams[key] = val;
      }
    });
    
    // Update URL first - the useEffect will then sync the state and call fetchReports
    updateUrlParams(filterParams);
  };

  const clearFilters = () => {
    // Clear URL and the useEffect will sync state and trigger fetchReports with empty params
    setSearchParams(new URLSearchParams());
    // Also clear temp date filters
    setTempDateFilters({ date_from: '', date_to: '' });
  };

  const handlePageChange = (newPage) => {
    // Build pagination parameters with current URL params (not state)
    const currentUrlParams = {};
    for (const [key, value] of searchParams.entries()) {
      if (key !== 'page' && key !== 'limit') { // Don't include existing page/limit
        currentUrlParams[key] = value;
      }
    }
    
    const paginationParams = {
      ...currentUrlParams,
      page: newPage,
      limit: pagination.limit || 20
    };
    
    // Update URL first, then the useEffect will trigger fetchReports
    updateUrlParams(paginationParams);
  };

  const handleViewReport = (reportId) => {
    try {
      navigate(`/admin/reported-problems/${reportId}`);
    } catch (err) {
      console.error('Navigation error:', err);
    }
  };

  const handleOpenAttachments = (attachments, index = 0) => {
    console.log("Attachements: ",attachments);
    setAttachmentsModalFiles(attachments);
    setAttachmentsModalIndex(index);
    setAttachmentsModalOpen(true);
  };
  console.log("Attachements: ",attachmentsModalFiles);
  // Get unique VC server hosts for dropdown
  const uniqueVcServerHosts = getUniqueVcServerHosts();

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">⏳ Loading reports...</div>
      </div>
    );
  }
  // console.log("Report: ",reports);
  if (error) {
    return (
      <div className="error-container">
        <h3>❌ Error Loading Reports</h3>
        <p>{error}</p>
        <details>
          <summary>Debug Info</summary>
          <pre>
            {JSON.stringify({ 
              reportsCount: reports.length, 
              filtersCount: Object.keys(filters).length,
              currentFilters: selectedFilters,
              searchTerm,
              urlParams: Object.fromEntries(searchParams),
              isInitialLoad
            }, null, 2)}
          </pre>
        </details>
        <button 
          onClick={() => fetchReports()}
          className="retry-btn"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="reports-container">
      {/* Header */}
      <div className="reports-header">
        <h1>📊 Reported Problems</h1>
        <div className="stats-section">
          <div className="stat-card">
            <h3>{pagination.total || 0}</h3>
            <p>Total Reports</p>
          </div>
          <div className="stat-card">
            <h3>{pagination.page || 1}</h3>
            <p>Current Page</p>
          </div>
          <div className="stat-card">
            <h3>{pagination.pages || 1}</h3>
            <p>Total Pages</p>
          </div>
        </div>
      </div>

      {/* Filters Section */}
      <div className="filters-section">
        {/* Search Section */}
        <div className="search-section">
          <form onSubmit={handleSearch}>
            <input
              type="text"
              placeholder="Search issues..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field"
            />
            <button type="submit" className="btn-primary">Search</button>
          </form>
        </div>

        {/* Date Filters */}
        <div className="date-filter-row">
          <div className="date-inputs">
            <label>From:</label>
            <input
              type="date"
              value={tempDateFilters.date_from ? tempDateFilters.date_from.split('T')[0] : ''}
              onChange={(e) => handleDateFilterChange('date_from', e.target.value ? new Date(e.target.value).toISOString() : '')}
              className="date-input"
            />

            <label>To:</label>
            <input
              type="date"
              value={tempDateFilters.date_to ? tempDateFilters.date_to.split('T')[0] : ''}
              onChange={(e) => handleDateFilterChange('date_to', e.target.value ? new Date(e.target.value).toISOString() : '')}
              className="date-input"
            />

            <button 
              onClick={applyDateFilters}
              className="btn-primary"
              disabled={!tempDateFilters.date_from && !tempDateFilters.date_to}
            >
              Apply
            </button>
          </div>
        </div>

        {/* Other Filters */}
        <div className="filter-row">
          <select
            value={selectedFilters.status_id}
            onChange={(e) => handleFilterChange('status_id', e.target.value)}
            className="select-field"
          >
            <option value="">All Statuses</option>
            {filters.status_options?.map(status => (
              <option key={status.id} value={status.id}>{status.name}</option>
            ))}
          </select>

          <select
            value={selectedFilters.issue_category_id}
            onChange={(e) => handleFilterChange('issue_category_id', e.target.value)}
            className="select-field"
          >
            <option value="">All Categories</option>
            {filters.category_options?.map(category => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>

          <select
            value={selectedFilters.reporter_type}
            onChange={(e) => handleFilterChange('reporter_type', e.target.value)}
            className="select-field"
          >
            <option value="">All Reporter Types</option>
            {filters.reporter_type_options?.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>

          <select
            value={selectedFilters.platform_name}
            onChange={(e) => handleFilterChange('platform_name', e.target.value)}
            className="select-field"
          >
            <option value="">All Platforms</option>
            {filters.platform_options?.map(platform => (
              <option key={platform.id} value={platform.name}>{platform.name}</option>
            ))}
          </select>

          <select
            value={selectedFilters.vc_server_host}
            onChange={(e) => handleFilterChange('vc_server_host', e.target.value)}
            className="select-field"
            title="Filter by VC Server Host"
          >
            <option value="">All VC Servers</option>
            {uniqueVcServerHosts.map(host => (
              <option key={host} value={host}>
                {host.length > 25 ? `${host.substring(0, 25)}...` : host}
              </option>
            ))}
          </select>

          <select
            value={selectedFilters.is_spam}
            onChange={(e) => handleFilterChange('is_spam', e.target.value)}
            className="select-field"
          >
            <option value="">All Reports</option>
            {filters.spam_options?.map(spam => (
              <option key={spam.value} value={spam.value}>{spam.label}</option>
            ))}
          </select>

          <button onClick={clearFilters} className="btn-secondary">
            Clear
          </button>
        </div>
      </div>

      {/* Reports Table */}
      <div className="table-container">
        <table className="reports-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Meeting</th>
              <th>Reporter</th>
              <th>Type</th>
              <th>Platform</th>
              <th>VC Server</th>
              <th>Category</th>
              <th>Status</th>
              <th>Description</th>
              <th>Spam</th>
              <th>Created</th>
              <th>Attachments</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {reports.length === 0 ? (
              <tr>
                <td colSpan={12} className="no-data">
                  📝 No reports found matching your criteria.
                  <br />
                  <small>Try adjusting your filters or search terms.</small>
                </td>
              </tr>
            ) : (
              reports.map(report => (
                <tr key={report.id}>
                  <td>#{report.id}</td>
                  <td>
                    <div className="meeting-cell">
                      <strong>{report.meeting_name}</strong>
                      <small>{report.meeting_uid}</small>
                      <small>Host: {report.meeting_host}</small>
                    </div>
                  </td>
                  <td>
                    <div className="reporter-cell">
                      <strong>{report.reporter_name}</strong>
                      <small>{report.reporter_email}</small>
                    </div>
                  </td>
                  <td>
                    <span className="type-tag">{report.reporter_type}</span>
                  </td>
                  <td>{report.platform_name}</td>
                  <td>
                    <div className="vc-server-cell">
                      {report.vc_server_host || 'N/A'}
                    </div>
                  </td>
                  <td>{report.issue_category_name}</td>
                  <td>
                    <span
                      className="status-tag"
                      style={{ backgroundColor: report.status_color }}
                    >
                      {report.status_name}
                    </span>
                  </td>
                  <td>
                    <div className="description-cell">
                      {(() => {
                        const cleanDescription = parseDescriptionWithoutLocation(report.user_description);
                        return (
                          <>
                            {cleanDescription?.substring(0, 80)}
                            {cleanDescription?.length > 80 && '...'}
                          </>
                        );
                      })()}
                    </div>
                  </td>
                  <td>
                    <span
                      className={`spam-tag ${report.is_spam ? 'spam' : 'not-spam'}`}
                    >
                      {report.is_spam_text}
                    </span>
                  </td>
                  <td>
                    <div className="date-cell">
                      <small>{new Date(report.created_at).toLocaleDateString()}</small>
                      <small>{new Date(report.created_at).toLocaleTimeString()}</small>
                    </div>
                  </td>
                  <td>
                    {Array.isArray(report.attachments) && report.attachments.length > 0 ? (
                      <button
                        className="btn-attachments"
                        onClick={() => handleOpenAttachments(
                          report.attachments.map(att => ({
                            ...att,
                            file_url: att.file_id || att.blob_url,
                            filename: att.filename || att.file_id || 'Attachment'
                          }))
                        )}
                        title="View Attachments"
                      >
                        View [{report.attachments.length}]
                      </button>
                    ) : (
                      <span style={{ color: '#aaa' }}>—</span>
                    )}
                  </td>
                  <td>
                    <button 
                      onClick={() => handleViewReport(report.id)}
                      className="btn-success"
                      title={`View Report #${report.id}`}
                    >
                      View
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="pagination-section">
          <div className="pagination-info">
            <span>
              Showing {reports.length} of {pagination.total} results 
              (Page {pagination.page} of {pagination.pages})
            </span>
          </div>
          <div className="pagination-controls">
            <button 
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="btn-primary"
            >
              Previous
            </button>
            <span className="page-info">
              Page {pagination.page} of {pagination.pages}
            </span>
            <button 
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.pages}
              className="btn-primary"
            >
              Next
            </button>
          </div>
        </div>
      )}

      <AttachmentsViewer
        isOpen={attachmentsModalOpen}
        onClose={() => setAttachmentsModalOpen(false)}
        attachments={attachmentsModalFiles}
        initialIndex={attachmentsModalIndex}
      />
    </div>
  );
}

export default Reports;