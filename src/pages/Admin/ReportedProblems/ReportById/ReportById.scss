.report-detail-container {
  max-width: 1200px;
  margin: 2rem auto;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 2rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  @media (max-width: 768px) {
    margin: 1rem;
    padding: 1rem;
  }
}

.report-header {
  margin-bottom: 2rem;

  .back-button {
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    background: #f3f4f6;
    color: #374151;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;

    &:hover {
      background: #e5e7eb;
      transform: translateY(-1px);
    }
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;

    h1 {
      font-size: 2rem;
      font-weight: 700;
      color: #1f2937;
      margin: 0;
    }
  }

  .divider {
    height: 1px;
    background: linear-gradient(to right, #e5e7eb, transparent);
    margin: 1.5rem 0;
  }
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
  text-transform: capitalize;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &.btn-primary {
      background: #3b82f6;
      color: white;

      &:hover {
        background: #2563eb;
      }
    }

    &.btn-secondary {
      background: #6b7280;
      color: white;

      &:hover {
        background: #4b5563;
      }
    }

    &.btn-success {
      background: #10b981;
      color: white;

      &:hover {
        background: #059669;
      }
    }

    &.btn-danger {
      background: #ef4444;
      color: white;

      &:hover {
        background: #dc2626;
      }
    }
  }
}

.main-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.detail-item {
  .label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .value {
    color: #1f2937;
    font-weight: 500;
    word-break: break-words;
    line-height: 1.5;
  }

  .badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: capitalize;

    &.reporter-type {
      background: #dbeafe;
      color: #1e40af;
    }

    &.spam-yes {
      background: #fee2e2;
      color: #dc2626;
    }

    &.spam-no {
      background: #dcfce7;
      color: #16a34a;
    }
  }
}

.description-section {
  margin: 2rem 0;

  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
  }

  .description-content {
    padding: 1.5rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    color: #374151;
    line-height: 1.7;
    min-height: 80px;
    white-space: pre-wrap;

    .no-description {
      font-style: italic;
      color: #9ca3af;
    }
  }
}

.attachments-section {
  margin: 2rem 0;

  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
  }

  .attachments-list.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem 0;
  }

  .image-thumbnail {
    display: block;
    width: 180px;
    height: 120px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    border: 2px solid #e5e7eb;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
  }
}


.resolution-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
  border: 1px solid #bbf7d0;
  border-radius: 12px;

  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #166534;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:before {
      content: "✅";
      font-size: 1.25rem;
    }
  }

  .resolution-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}

.loading-state {
  text-align: center;
  padding: 4rem 2rem;

  .loading-text {
    font-size: 1.125rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }

  .loading-subtext {
    font-size: 0.875rem;
    color: #9ca3af;
  }

  .spinner {
    margin: 2rem auto;
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.error-state {
  padding: 2rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  margin: 2rem;

  h2 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #dc2626;
    margin: 0 0 0.5rem 0;
  }

  p {
    color: #b91c1c;
    margin-bottom: 1rem;
  }
}

.not-found-state {
  text-align: center;
  padding: 4rem 2rem;

  h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  p {
    color: #6b7280;
    margin-bottom: 2rem;
  }
}

// Edit Form Styles
.edit-form {
  margin-top: 2rem;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &:before {
      content: "✏️";
      font-size: 1.25rem;
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .form-group {
    .form-label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
    }

    .form-input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 0.875rem;
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    .form-select {
      @extend .form-input;
      background: white;
      cursor: pointer;
    }

    .form-textarea {
      @extend .form-input;
      min-height: 100px;
      resize: vertical;
    }
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    flex-wrap: wrap;
  }
}

.success-message {
  padding: 1rem 1.5rem;
  background: #ecfdf5;
  border: 1px solid #bbf7d0;
  color: #166534;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:before {
    content: "✅";
    font-size: 1rem;
  }
}

.error-message {
  padding: 1rem 1.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:before {
    content: "❌";
    font-size: 1rem;
  }
}

.form-help {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
  font-style: italic;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive Design
@media (max-width: 768px) {
  .main-details {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-buttons {
    .btn {
      flex: 1;
      justify-content: center;
    }
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    .btn {
      flex: 1;
    }
  }
}
.attachments-list.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem 0;
}

.image-thumbnail {
  display: block;
  width: 120px;
  height: 90px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  border: 2px solid #e5e7eb;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}
