import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import { AdminReportsServices } from "../../../../services/Admin/ReportedProblems/Reports/index.service";
import './ReportById.scss';

import { selectUserData } from "../../../../redux/AuthSlice/index.slice";

function DetailItem({ label, children }) {
  return (
    <div className="detail-item">
      <p className="label">{label}</p>
      <div className="value">{children}</div>
    </div>
  );
}

function ReportById() {
  const userData = useSelector(selectUserData);
  const { reportId } = useParams();
  const navigate = useNavigate();
  const [report, setReport] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [updateMessage, setUpdateMessage] = useState(null);
  const [updateError, setUpdateError] = useState(null);

  // Options from API
  const [statusOptions, setStatusOptions] = useState([]);
  const [categoryOptions, setCategoryOptions] = useState([]);

  // Form state for editing
  const [editForm, setEditForm] = useState({
    status_name: '',
    is_spam: '',
    resolved_by: '',
    issue_category_name: '',
  });


  const [emailForm, setEmailForm] = useState({
    sendEmail: true, // Changed to true by default
    resolutionDetails: '',
  });


  const accountName = userData?.userData?.full_name || 'Admin User';

  
  const parseDescriptionWithoutLocation = (description) => {
    if (!description) return '';
    
    const cleanedDescription = description.replace(/\n\n\[LOCATION: .+?\]$/, '');
    return cleanedDescription.trim();
  };


  const extractLocationFromDescription = (description) => {
    if (!description) return null;
    
    const locationMatch = description.match(/\[LOCATION: (.+?)\]/);
    return locationMatch ? locationMatch[1] : null;
  };


  const isStatusFinal = () => {

    if (isEditing && editForm.status_name) {
      const selectedStatus = statusOptions.find(status => status.name === editForm.status_name);
      return selectedStatus?.is_final === true;
    }
    
    return report?.status?.is_final === true;
  };

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const [reportRes, statusRes, categoryRes] = await Promise.all([
          AdminReportsServices.getReportById(reportId),
          AdminReportsServices.getStatuses(),
          AdminReportsServices.getIssueCategories()
        ]);

        // Handle report data
        if (reportRes?.success === 1 && reportRes.data?.report) {
          const reportData = reportRes.data.report;
          setReport(reportData);

          setEditForm({
            status_name: reportData.status?.name || '',
            is_spam: reportData.spam_info?.is_spam ? 'yes' : 'no',
            resolved_by: reportData.resolution?.resolved_by || '',
            issue_category_name: reportData.issue?.category_name || '',
          });
        } else {
          setError("Report not found or invalid response structure.");
        }


        if (statusRes?.success === 1 && statusRes.data?.statuses) {
          setStatusOptions(statusRes.data.statuses);
        }


        if (categoryRes?.success === 1 && categoryRes.data?.categories) {
          setCategoryOptions(categoryRes.data.categories);
        }

      } catch (err) {
        setError(`Failed to load report: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }

    if (reportId) {
      fetchData();
    } else {
      setError("No report ID provided");
      setLoading(false);
    }
  }, [reportId]);

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    setUpdateMessage(null);
    setUpdateError(null);
  };

  const handleFormChange = (field, value) => {
    setEditForm(prev => {
      const updatedForm = { ...prev, [field]: value };

      if (field === "status_name" && value.toLowerCase() === "closed") {
        updatedForm.resolved_by = accountName;
      }

      return updatedForm;
    });
  };

  const handleEmailFormChange = (field, value) => {
    setEmailForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleUpdate = async () => {
    setUpdateLoading(true);
    setUpdateMessage(null);
    setUpdateError(null);

    try {
      const updateData = {};

      Object.keys(editForm).forEach(key => {
        if (editForm[key] !== '' && editForm[key] !== null) {
          updateData[key] = editForm[key];
        }
      });

      if (!updateData.resolved_by || updateData.resolved_by.trim() === '') {
        delete updateData.resolved_by;
      }

      const response = await AdminReportsServices.updateReport(reportId, updateData);

      if (response?.success === 1) {
        setUpdateMessage('Report updated successfully!');
        setIsEditing(false);

        const updatedReport = await AdminReportsServices.getReportById(reportId);
        if (updatedReport?.success === 1 && updatedReport.data?.report) {
          setReport(updatedReport.data.report);
          
          const reportData = updatedReport.data.report;
          setEditForm({
            status_name: reportData.status?.name || '',
            is_spam: reportData.spam_info?.is_spam ? 'yes' : 'no',
            resolved_by: reportData.resolution?.resolved_by || '',
            issue_category_name: reportData.issue?.category_name || '',
          });
        }

        if (emailForm.sendEmail && report?.reporter?.email) {
          try {
            console.log('Sending email to:', report.reporter.email);
            
            const emailData = {
              user_email: report.reporter.email,
              issue_title: report.issue?.category_name || "Report Issue",
              issue_description: report.issue?.description || "Issue reported by user",
              resolution_details: emailForm.resolutionDetails || "Your issue has been resolved.",
              admin_name: accountName,
            };

            console.log('Email data:', emailData);

            const emailResponse = await AdminReportsServices.sendIssueResolutionEmail(emailData);

            console.log('Email response:', emailResponse);

            if (emailResponse?.success === 1) {
              setUpdateMessage('Report updated and resolution email sent successfully!');
            } else {
              setUpdateMessage('Report updated successfully, but failed to send email.');
            }
          } catch (emailErr) {
            console.error('Email sending error:', emailErr);
            setUpdateMessage('Report updated successfully, but failed to send email.');
          }
        } else {
          console.log('Email not sent because:', {
            sendEmail: emailForm.sendEmail,
            hasEmail: !!report?.reporter?.email,
            email: report?.reporter?.email
          });
        }
      } else {
        setUpdateError(response?.message || 'Failed to update report');
      }
    } catch (err) {
      setUpdateError(err.message);
    } finally {
      setUpdateLoading(false);
    }
  };

  const isStatusFinalValue = isStatusFinal();
  
  // // Debug logging
  // console.log('Status options:', statusOptions);
  // console.log('Is editing:', isEditing);
  // console.log('Edit form status:', editForm.status_name);
  // console.log('Is status final:', isStatusFinalValue);

  if (loading) {
    return (
      <div className="report-detail-container">
        <div className="loading-state">
          <div className="spinner"/>
          <p className="loading-text">Loading report details...</p>
          <p className="loading-subtext">Report ID: {reportId}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="report-detail-container">
        <div className="error-state">
          <h2>Error</h2>
          <p>{error}</p>
          <button onClick={() => navigate(-1)} className="btn btn-primary">
            ← Back
          </button>
        </div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="report-detail-container">
        <div className="not-found-state">
          <h2>Report Not Found</h2>
          <p>Report ID: {reportId}</p>
          <button onClick={() => navigate(-1)} className="btn btn-primary">
            ← Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="report-detail-container">
      {/* Header */}
      <div className="report-header">
        <button onClick={() => navigate(-1)} className="back-button">
          ← Back to Reports
        </button>
        <div className="header-content">
          <h1>Report #{report.id}</h1>
          {report.status?.name && (
            <span
              className="status-badge"
              style={{ backgroundColor: report.status.color_code || '#6B7280' }}
            >
              {report.status.name}
            </span>
          )}
        </div>
        <div className="divider"/>
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        <button 
          onClick={handleEditToggle} 
          className={`btn ${isEditing ? 'btn-secondary' : 'btn-primary'}`}
        >
          {isEditing ? 'Cancel' : 'Edit'}
        </button>
      </div>

      {/* Success/Error Messages */}
      {updateMessage && (
        <div className="success-message">
          {updateMessage}
        </div>
      )}
      {updateError && (
        <div className="error-message">
          {updateError}
        </div>
      )}

      {/* Main Details Section */}
      <div className="main-details">
        <DetailItem label="Meeting">
          {report.meeting?.name} ({report.meeting?.room_uid})
        </DetailItem>
        <DetailItem label="Meeting Host">
          {report.meeting?.host}
        </DetailItem>
        <DetailItem label="Reporter">
          {report.reporter?.name} ({report.reporter?.email})
        </DetailItem>
        <DetailItem label="Reporter Type">
          <span className="badge reporter-type">
            {report.reporter?.type}
          </span>
        </DetailItem>
        <DetailItem label="Platform">
          {report.platform?.name}
        </DetailItem>
        <DetailItem label="VC Server">
          {report.technical_details?.vc_server_host || "N/A"}
        </DetailItem>
        <DetailItem label="Category">
          {report.issue?.category_name}
        </DetailItem>
        <DetailItem label="Spam Status">
          <span className={`badge ${report.spam_info?.is_spam ? 'spam-yes' : 'spam-no'}`}>
            {report.spam_info?.is_spam_text}
          </span>
        </DetailItem>
        <DetailItem label="Created At">
          {new Date(report.timestamps?.created_at).toLocaleString()}
        </DetailItem>
        <DetailItem label="Updated At">
          {new Date(report.timestamps?.updated_at).toLocaleString()}
        </DetailItem>
        {report.resolution?.resolved_by && (
          <DetailItem label="Resolved By">
            {report.resolution.resolved_by}
          </DetailItem>
        )}
        {report.resolution?.resolved_at && (
          <DetailItem label="Resolved At">
            {new Date(report.resolution.resolved_at).toLocaleString()}
          </DetailItem>
        )}
      </div>

      {/* Description Section */}
      <div className="description-section">
        <h3>Description</h3>
        <div className="description-content">
          {(() => {
            const cleanDescription = parseDescriptionWithoutLocation(report.issue?.description);
            return cleanDescription || (
              <span className="no-description">No description provided.</span>
            );
          })()}
        </div>
      </div>

      {/* Location Section - Show only if location exists */}
      {(() => {
        const location = extractLocationFromDescription(report.issue?.description);
        return location ? (
          <div className="location-section">
            <h5>Location</h5>
            <div className="location-content">
              <span className="location-badge">
                {location}
              </span>
            </div>
          </div>
        ) : null;
      })()}

      {/* Attachments Section */}
      {report.attachments?.length > 0 && (
        <div className="attachments-section">
          <h3>Attachments</h3>
          <div className="attachments-list image-grid">
            {report.attachments.map((att) => (
              <a
                key={att.id}
                href={att.file_id}
                target="_blank"
                rel="noopener noreferrer"
                className="image-thumbnail"
              >
                <img src={att.file_id} alt={`Attachment ${att.id}`} />
              </a>
            ))}
          </div>
        </div>
      )}

      {/* Debug Information - Add this section temporarily */}
      {/* {isEditing && (
        <div style={{ 
          background: '#f0f0f0', 
          padding: '10px', 
          margin: '10px 0', 
          border: '1px solid #ccc',
          fontSize: '12px'
        }}>
          <strong>Debug Info:</strong><br/>
          Status Options Count: {statusOptions.length}<br/>
          Current Status: {report?.status?.name}<br/>
          Current Status is_final: {report?.status?.is_final ? 'true' : 'false'}<br/>
          Selected Status: {editForm.status_name}<br/>
          Is Status Final: {isStatusFinalValue ? 'true' : 'false'}<br/>
          Is Editing: {isEditing ? 'true' : 'false'}<br/>
          Email Form Send Email: {emailForm.sendEmail ? 'true' : 'false'}
        </div>
      )} */}

      {/* Edit Form */}
      {isEditing && (
        <div className="edit-form">
          <h3>Edit Report</h3>
          <div className="form-grid">
            <div className="form-group">
              <label className="form-label">Status</label>
              <select
                className="form-select"
                value={editForm.status_name}
                onChange={(e) => handleFormChange('status_name', e.target.value)}
              >
                <option value="">Select Status</option>
                {statusOptions.map(status => (
                  <option key={status.id} value={status.name}>
                    {status.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Issue Category</label>
              <select
                className="form-select"
                value={editForm.issue_category_name}
                onChange={(e) => handleFormChange('issue_category_name', e.target.value)}
              >
                <option value="">Select Category</option>
                {categoryOptions.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Spam Status</label>
              <select
                className="form-select"
                value={editForm.is_spam}
                onChange={(e) => handleFormChange('is_spam', e.target.value)}
              >
                <option value="">Select Spam Status</option>
                <option value="yes">Yes</option>
                <option value="no">No</option>
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Resolved By</label>
              <input
                type="text"
                className="form-input"
                value={editForm.resolved_by}
                onChange={(e) => handleFormChange('resolved_by', e.target.value)}
                placeholder="Will be auto-filled when status is Closed"
                disabled
                style={{ backgroundColor: '#f3f4f6', cursor: 'not-allowed' }}
              />
              <small className="form-help">
                This field is automatically updated when status is changed to &quot;Closed&quot;
              </small>
            </div>
          </div>

          {/* Email Notification Section - Only show when status is final */}
          {(isStatusFinalValue || true) && (
            <div className="email-notification-section">
              <h4>Issue Resolution Email</h4>
              <div className="email-form">
                <div className="form-group">
                  <label className="form-label">
                    <input
                      type="checkbox"
                      checked={emailForm.sendEmail}
                      onChange={(e) => handleEmailFormChange('sendEmail', e.target.checked)}
                      className="form-checkbox"
                    />
                    Send resolution email to reporter
                  </label>
                  {report.reporter?.email && (
                    <small className="form-help">
                      Email will be sent to: {report.reporter.email}
                    </small>
                  )}
                  {!report.reporter?.email && (
                    <small className="form-help error">
                      No email address available for reporter
                    </small>
                  )}
                </div>

                {emailForm.sendEmail && (
                  <div className="form-group">
                    <label className="form-label">Resolution Details (Optional)</label>
                    <textarea
                      className="form-textarea"
                      value={emailForm.resolutionDetails}
                      onChange={(e) => handleEmailFormChange('resolutionDetails', e.target.value)}
                      placeholder="Provide detailed resolution information for the user (optional)..."
                      rows={4}
                    />
                    <small className="form-help">
                      This information will be included in the email sent to the reporter. If left empty, a default message will be used.
                    </small>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="form-actions">
            <button 
              onClick={handleUpdate} 
              className="btn btn-success"
              disabled={updateLoading}
            >
              {updateLoading ? '⏳ Updating...' : 'Save'}
            </button>
            <button 
              onClick={handleEditToggle} 
              className="btn btn-secondary"
              disabled={updateLoading}
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default ReportById;