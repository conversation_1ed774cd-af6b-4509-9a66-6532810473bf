import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>crumb,
  ModalComponent,
} from "../../../../components";
import { AdminSocialMediaLinksServices } from "../../../../services"; // Changed this import
import moduleRoutesMap from "../../../../routeControl";
import { logger, modalNotification } from "../../../../utils";

function SocialMediaLinks() {
  const [urls, setUrls] = useState({
    instagram: "",
    twitter: "",
    facebook: "",
    linkedin: "",
  });
  const [originalUrls, setOriginalUrls] = useState({});
  const [isUpdated, setIsUpdated] = useState(false);
  const [loading, setLoading] = useState(false);
  const [socialLinkId, setSocialLinkId] = useState(null);
 
  useEffect(() => {
    const fetchUrls = async () => {
      setLoading(true);
      try {
        const res = await AdminSocialMediaLinksServices.getSocialMediaLinks();
        if (res?.success === 1) {
          const data = res.data || {
            instagram: "",
            twitter: "",
            facebook: "",
            linkedin: "",
          };
          setUrls(data);
          setOriginalUrls(data);
          setSocialLinkId(res.data?.id || null);
        } else {
          modalNotification({
            type: "error",
            message: res?.message || "Failed to fetch social media links",
          });
        }
      } catch (error) {
        logger(error);
        modalNotification({
          type: "error",
          message: "Error fetching social media links",
        });
      }
      setLoading(false);
    };

    fetchUrls();
  }, []);

  // Enhanced change handler to properly detect changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    const updatedUrls = { ...urls, [name]: value };
    setUrls(updatedUrls);
    
    // Check if any field has changed from original
    const hasChanges = Object.keys(updatedUrls).some(
      key => updatedUrls[key] !== originalUrls[key]
    );
    setIsUpdated(hasChanges);
  };

  const handleUpdate = async () => {
    if (!isUpdated || loading) return;
    
    setLoading(true);
    try {
      const res = await AdminSocialMediaLinksServices.updateSocialMediaLinks({ // Updated service call
        id: socialLinkId,
        ...urls
      });
      if (res?.success === 1) {
        setOriginalUrls(urls);
        setIsUpdated(false);
        modalNotification({
          type: "success",
          message: res?.message || "Social media links updated successfully",
        });
      } else {
        modalNotification({
          type: "error",
          message: res?.message || "Failed to update social media links",
        });
      }
    } catch (error) {
      logger(error);
      modalNotification({
        type: "error",
        message: "Error updating social media links",
      });
    }
    setLoading(false);
  };

  const breadcrumb = [
    {
      path: moduleRoutesMap.admin.DASHBOARD.path,
      name: "DASHBOARD",
    },
    {
      path: "#",
      name: "SOCIAL MEDIA LINKS",
    },
  ];

  return (
    <>
      <div className="nk-block-head nk-block-head-sm">
        <div className="nk-block-between">
          <PageHeader heading="Social Media Links">
            <Breadcrumb breadcrumb={breadcrumb} />
          </PageHeader>
        </div>
      </div>
      
      <div className="nk-block">
        <div className="card card-bordered">
          <div className="card-inner">
            <div className="row g-4">
              <div className="col-lg-6">
                <div className="form-group">
                  <label className="form-label" htmlFor="instagram">
                    Instagram URL
                  </label>
                  <div className="form-control-wrap">
                    <input
                      type="url"
                      id="instagram"
                      name="instagram"
                      value={urls.instagram}
                      onChange={handleChange}
                      className="form-control"
                      placeholder="https://instagram.com/username"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>
              
              <div className="col-lg-6">
                <div className="form-group">
                  <label className="form-label" htmlFor="twitter">
                    Twitter URL
                  </label>
                  <div className="form-control-wrap">
                    <input
                      type="url"
                      id="twitter"
                      name="twitter"
                      value={urls.twitter}
                      onChange={handleChange}
                      className="form-control"
                      placeholder="https://twitter.com/username"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>
              
              <div className="col-lg-6">
                <div className="form-group">
                  <label className="form-label" htmlFor="facebook">
                    Facebook URL
                  </label>
                  <div className="form-control-wrap">
                    <input
                      type="url"
                      id="facebook"
                      name="facebook"
                      value={urls.facebook}
                      onChange={handleChange}
                      className="form-control"
                      placeholder="https://facebook.com/username"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>
              
              <div className="col-lg-6">
                <div className="form-group">
                  <label className="form-label" htmlFor="linkedin">
                    LinkedIn URL
                  </label>
                  <div className="form-control-wrap">
                    <input
                      type="url"
                      id="linkedin"
                      name="linkedin"
                      value={urls.linkedin}
                      onChange={handleChange}
                      className="form-control"
                      placeholder="https://linkedin.com/company/username"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="form-group mt-4">
              <button
                type="button"
                className={`btn ${isUpdated && !loading ? 'btn-primary' : 'btn-secondary'}`}
                onClick={handleUpdate}
                disabled={!isUpdated || loading}
                style={{ 
                  opacity: (!isUpdated || loading) ? 0.6 : 1,
                  cursor: (!isUpdated || loading) ? 'not-allowed' : 'pointer'
                }}
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status"/>
                    Updating...
                  </>
                ) : (
                  <>
                    <em className="icon ni ni-save me-1"/>
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {loading && (
        <ModalComponent
          backdrop
          show={loading}
          title="Loading..."
        >
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-2">Please wait...</p>
          </div>
        </ModalComponent>
      )}
    </>
  );
}

export default SocialMediaLinks;