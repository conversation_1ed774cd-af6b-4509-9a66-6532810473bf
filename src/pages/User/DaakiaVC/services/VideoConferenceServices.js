import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../API/axios";
import { Endpoints } from "../API/Endpoints/routes";
// import { apiLogger } from "../utils/helper";

export const VideoConferenceService = {
  sipConnection: async (id, user, token=null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.sip_connection.method,
        endpoint: Endpoints.sip_connection.url,
        payload: {
          meeting_uid: id,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error connecting to SIP", {
          response,
          payload: {
            meeting_uid: id,
          },
          endpoint: Endpoints.sip_connection.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success connecting to SIP", {
          response,
          payload: {
            meeting_uid: id,
          },
          endpoint: Endpoints.sip_connection.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getScreenSharePermission: async (id) => {
    try {
      const endpoint = Endpoints.get_screen_share_permission(id);
      const response = await APIrequest({
        method: endpoint.method,
        endpoint: endpoint.url,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting screen share permission", {
          response,
          payload: {
            meeting_uid: id,
          },
          endpoint: endpoint.url,
        });
      } else {
        datadogLogs.logger.info("Success getting screen share permission", {
          response,
          payload: {
            meeting_uid: id,
          },
          endpoint: endpoint.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  updateScreenSharePermission: async (id, checked) => {
    try {
      const endpoint = Endpoints.update_screen_share_permission(id);
      const response = await APIrequest({
        method: endpoint.method,
        endpoint: endpoint.url,
        payload: {
          meeting_id: id,
          permission_granted: checked,
        },
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error updating screen share permission", {
          response,
          payload: {
            meeting_uid: id,
          },
        });
      } else {
        datadogLogs.logger.info("Success updating screen share permission", {
          response,
          payload: {
            meeting_uid: id,
          },
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  }
};
