import React, { useState, useEffect } from "react";
import { MailOutlined } from '@ant-design/icons';
import <PERSON><PERSON> from "lottie-react";
import { Button, Input } from "antd";
import TextArea from "antd/lib/input/TextArea";
import SideDrawer from "../SideDrawer";
import sendMessageAnimation from "./icons/success.json";

import { SettingsMenuServices } from "../../services/SettingsMenuServices";
import "../../styles/ReportProblem.scss";
import "../../styles/index.scss";
import { useVideoConferencesContext } from "../../context/VideoConferencesContext";
import { useSaasHelpers } from "../../SaaS/helpers/helpers";

export function ReportProblemDrawer({
  isRPDrawerOpen,
  id,
  clientPreferedServerId,
  isWhiteboardOpen,
  localParticipant,
  setToastNotification,
  setToastStatus,
  setShowToast,
  meetingFeatures,
  issueCategories,
  categoriesLoading,
}) {
  const { setOpenDrawer } = useVideoConferencesContext();
  const [checkedItems, setCheckedItems] = useState({});
  const [problemReported, setProblemReported] = useState(false);
  const [textAreaValue, setTextAreaValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const { saasHostToken } = useSaasHelpers();

  const [emailValue, setEmailValue] = useState("");
  const [emailEnabled, setEmailEnabled] = useState(false);

  useEffect(() => {
    console.log("localParticipant", localParticipant);
    if (isRPDrawerOpen && Array.isArray(issueCategories)) {
      const initialCheckedState = {};
      issueCategories.forEach((category) => {
        if (category && category.id) {
          initialCheckedState[category.id] = false;
        }
      });
      setCheckedItems(initialCheckedState);
          
      setEmailEnabled(false);
      
      
    }
  }, [isRPDrawerOpen, issueCategories, localParticipant]);

  const onChange = (categoryId) => {
    const currentlySelected = Object.keys(checkedItems).filter(
      (catId) => checkedItems[catId]
    ).length;

    if (!checkedItems[categoryId] && currentlySelected >= 3) {
      setToastNotification("You can select a maximum of 3 categories.");
      setToastStatus("warning");
      setShowToast(true);
      return;
    }

    setCheckedItems((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }));
  };

  const handleTextChange = (e) => {
    setTextAreaValue(e.target.value);
  };

  const handleEmailChange = (e) => {
    setEmailValue(e.target.value);
  };

  const handleEmailToggle = () => {
    setEmailEnabled(!emailEnabled); 
  };

  const getClientIP = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();
      return data.ip;
    } catch (error) {
      return null;
    }
  };


  const getUserLocation = async () => {
    try {
      const getCurrentPosition = () => {
        return new Promise((resolve, reject) => {
          if (!navigator.geolocation) {
            reject(new Error('Geolocation is not supported by this browser'));
            return;
          }

          navigator.geolocation.getCurrentPosition(
            (position) => {
              resolve({
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                source: 'browser'
              });
            },
            (error) => {
              reject(error);
            },
            {
              enableHighAccuracy: false,
              timeout: 10000,
              maximumAge: 60000
            }
          );
        });
      };

      try {
        const location = await getCurrentPosition();
        
        const reverseGeocodeResponse = await fetch(
          `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${location.latitude}&longitude=${location.longitude}&localityLanguage=en`
        );
        
        if (reverseGeocodeResponse.ok) {
          const locationData = await reverseGeocodeResponse.json();
          return {
            latitude: location.latitude,
            longitude: location.longitude,
            city: locationData.city || locationData.locality || 'Unknown',
            country: locationData.countryName || 'Unknown',
            region: locationData.principalSubdivision || 'Unknown',
            source: 'browser'
          };
        }
        
        return {
          latitude: location.latitude,
          longitude: location.longitude,
          city: 'Unknown',
          country: 'Unknown',
          region: 'Unknown',
          source: 'browser'
        };
      } catch (browserError) {
        console.log('Browser geolocation failed, trying IP-based location');
        
        const clientIP = await getClientIP();
        if (clientIP) {
          const ipLocationResponse = await fetch(`https://api.bigdatacloud.net/data/ip-geolocation-full?ip=${clientIP}&localityLanguage=en`);
          
          if (ipLocationResponse.ok) {
            const ipLocationData = await ipLocationResponse.json();
            return {
              latitude: ipLocationData.location?.latitude || null,
              longitude: ipLocationData.location?.longitude || null,
              city: ipLocationData.location?.city || ipLocationData.location?.locality || 'Unknown',
              country: ipLocationData.location?.country?.name || 'Unknown',
              region: ipLocationData.location?.principalSubdivision?.name || 'Unknown',
              ip: clientIP,
              source: 'ip'
            };
          }
        }
        
        throw new Error('Unable to determine location');
      }
    } catch (error) {
      console.error('Error getting user location:', error);
      return {
        latitude: null,
        longitude: null,
        city: 'Unknown',
        country: 'Unknown',
        region: 'Unknown',
        source: 'failed'
      };
    }
  };

  const getDeviceInfo = () => {
    const ua = window.navigator.userAgent;
  
    let os = "Unknown";
    if (/windows nt/i.test(ua)) os = "Windows";
    else if (/mac os x/i.test(ua)) os = "MacOS";
    else if (/android/i.test(ua)) os = "Android";
    else if (/iphone|ipad|ipod/i.test(ua)) os = "iOS";
    else if (/linux/i.test(ua)) os = "Linux";
  
    let deviceType = "desktop";
    if (/mobi|android|iphone|ipad|ipod/i.test(ua)) deviceType = "mobile";
    else if (/tablet|ipad/i.test(ua)) deviceType = "tablet";
  
    let browser = "Unknown";
    let version = "Unknown";
  
    if (/brave/i.test(navigator.userAgent) || navigator.brave) {
      browser = "Brave";
      const match = ua.match(/Chrome\/([\d.]+)/);
      version = match ? match[1] : "Unknown";
    } else if (/edg/i.test(ua)) {
      browser = "Edge";
      const match = ua.match(/Edg\/([\d.]+)/);
      version = match ? match[1] : "Unknown";
    } else if (/chrome/i.test(ua)) {
      browser = "Chrome";
      const match = ua.match(/Chrome\/([\d.]+)/);
      version = match ? match[1] : "Unknown";
    } else if (/firefox/i.test(ua)) {
      browser = "Firefox";
      const match = ua.match(/Firefox\/([\d.]+)/);
      version = match ? match[1] : "Unknown";
    } else if (/safari/i.test(ua)) {
      browser = "Safari";
      const match = ua.match(/Version\/([\d.]+)/);
      version = match ? match[1] : "Unknown";
    }
  
    return {
      os,
      deviceType,
      browser,
      version,
      userAgent: ua,
    };
  };
  

  const handleFileSelect = () => {
    if (attachments.length >= 5) {
      setToastNotification("You can add a maximum of 5 attachments.");
      setToastStatus("warning");
      setShowToast(true);
      return;
    }

    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";
    fileInput.multiple = false;

    fileInput.onchange = (event) => {
      const file = event.target.files[0];

      if (file) {
        // Check file size (max 10MB)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
          setToastNotification("File size must be less than 10MB.");
          setToastStatus("error");
          setShowToast(true);
          return;
        }
        const existingFile = attachments.find(att => att.filename === file.name);
        if (existingFile) {
          setToastNotification("A file with this name is already added.");
          setToastStatus("warning");
          setShowToast(true);
          return;
        }
        const newAttachment = {
          id: Date.now(),
          filename: file.name,
          size: file.size,
          type: file.type,
          localFile: file, 
        };

        setAttachments((prev) => [...prev, newAttachment]);
        setToastNotification("File added successfully.");
        setToastStatus("success");
        setShowToast(true);
      }
    };

    fileInput.click();
  };

  const handleRemoveAttachment = (attachmentId) => {
    setAttachments((prev) => prev.filter((att) => att.id !== attachmentId));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  };

  const uploadAttachment = async (file) => {
    try {
      const reporterName = localParticipant?.participantInfo?.name ||
        localParticipant?.participantInfo?.displayName ||
        localParticipant?.name ||
        localParticipant?.displayName ||
        "Anonymous User";

      const result = await SettingsMenuServices.uploadAttachment(
        file, 
        reporterName, 
        saasHostToken
      );

      // Add more detailed logging
      if (!result.success) {
        console.error('Upload failed:', {
          filename: file.name,
          error: result.error,
          fileSize: file.size,
          fileType: file.type
        });
      }

      return result;
    } catch (error) {
      console.error('Upload error:', error);
      return {
        success: false,
        error: error.message || "Upload failed"
      };
    }
  };

  const handleSubmit = async () => {
    if (textAreaValue.length === 0) {
      setToastNotification("Please enter your concern.");
      setToastStatus("error");
      setShowToast(true);
      return;
    }

    const selectedCategories = Object.keys(checkedItems)
      .filter((categoryId) => checkedItems[categoryId])
      .map((key) => parseInt(key));

    if (selectedCategories.length === 0) {
      setToastNotification("Please select at least one issue category.");
      setToastStatus("error");
      setShowToast(true);
      return;
    }

    if (selectedCategories.length > 3) {
      setToastNotification("You can select a maximum of 3 categories.");
      setToastStatus("error");
      setShowToast(true);
      return;
    }

    // Validate email if enabled
    if (emailEnabled && (!emailValue || !emailValue.includes('@'))) {
      setToastNotification("Please enter a valid email address.");
      setToastStatus("error");
      setShowToast(true);
      return;
    }

    try {
      setLoading(true);


      const userLocation = await getUserLocation();
      
      let locationString = "";
      if (userLocation.city !== 'Unknown' || userLocation.country !== 'Unknown') {
        const locationParts = [];
        if (userLocation.city !== 'Unknown') locationParts.push(userLocation.city);
        if (userLocation.region !== 'Unknown' && userLocation.region !== userLocation.city) {
          locationParts.push(userLocation.region);
        }
        if (userLocation.country !== 'Unknown') locationParts.push(userLocation.country);
        
        locationString = locationParts.join(', ');
      }

      let uploadedAttachments = [];
      if (attachments.length > 0) {
        setToastNotification("Uploading attachments...");
        setToastStatus("info");
        setShowToast(true);

        const uploadPromises = attachments.map(async (attachment) => {
          const uploadResult = await uploadAttachment(attachment.localFile);
          if (!uploadResult.success) {
            throw new Error(`Failed to upload ${attachment.filename}: ${uploadResult.error}`);
          }
          return {
            originalFilename: attachment.filename,
            blob_url: uploadResult.blob_url,
            cdn_url: uploadResult.cdn_url,
            filename: uploadResult.filename,
            size: uploadResult.size,
            mimetype: uploadResult.mimetype
          };
        });

        uploadedAttachments = await Promise.all(uploadPromises);
        console.log("Uploaded attachments:", uploadedAttachments);
      }

      const clientIP = await getClientIP();
      const deviceInfo = getDeviceInfo();

      const reporterEmail = emailEnabled ? emailValue : (
        localParticipant?.participantInfo?.email ||
        localParticipant?.email ||
        "<EMAIL>"
      );

      const reporterName =
        localParticipant?.participantInfo?.name ||
        localParticipant?.participantInfo?.displayName ||
        localParticipant?.name ||
        localParticipant?.displayName ||
        "Anonymous User";

      let parsedMetadata = {};
      try {
        parsedMetadata = localParticipant?.metadata
          ? JSON.parse(localParticipant.metadata)
          : {};
      } catch (e) {
        console.error("Error parsing metadata:", e);
        parsedMetadata = {};
      }

      console.log("Parsed metadata:", parsedMetadata);

      const roleType = parsedMetadata?.role_name || "participant";

      let reporterType = "participant";

      if (roleType === "moderator") {
        reporterType = "host";
      } else if (roleType === "cohost") {
        reporterType = "cohost";
      } else {
        reporterType = "participant";
      }

      const platformName = `${deviceInfo.deviceType} ${deviceInfo.os} ${deviceInfo.browser} ${deviceInfo.version}` ;
      
      const attachmentPayload = uploadedAttachments.map((att) => ({
        filename: att.cdn_url,
        blob_url: att.blob_url,
        size: att.size,
        mimetype: att.mimetype
      }));

      let finalDescription = textAreaValue;
      if (locationString) {
        finalDescription = `${textAreaValue}\n\n[LOCATION: ${locationString}]`;
      }

      const reportPromises = selectedCategories.map((categoryId) => {
        const payload = {
          meeting_uid: id,
          session_id: localParticipant?.sessionId || null,
          reporter_email: reporterEmail,
          reporter_name: reporterName,
          reporter_type: reporterType,
          platform_name: platformName,
          issue_category_id: categoryId,
          issue_id: null,
          user_description: finalDescription, 
          client_ip: clientIP,
          vc_server_host: clientPreferedServerId,
          attachments: attachmentPayload, 
        };

        console.log("Report payload:", payload);

        return SettingsMenuServices.reportAProblem(
          payload,
          localParticipant?.participantInfo,
          saasHostToken
        );
      });

      const responses = await Promise.all(reportPromises);

      const failedResponses = responses.filter(
        (response) => response.success === 0
      );

      if (failedResponses.length > 0) {
        const errorMessage =
          failedResponses[0].message || "Some reports failed to submit";
        setToastNotification(errorMessage);
        setToastStatus("error");
        setShowToast(true);
        throw new Error(errorMessage);
      } else {
        setProblemReported(true);
      }
    } catch (error) {
      setToastNotification(
        error.message || "An error occurred while submitting the report"
      );
      setToastStatus("error");
      setShowToast(true);
    } finally {
      setLoading(false);
      setTimeout(() => {
        setOpenDrawer(null);
        setProblemReported(false);
        setTextAreaValue("");
        setAttachments([]);
        setEmailValue("");
        setEmailEnabled(false);
        const resetCheckedState = {};
        if (Array.isArray(issueCategories)) {
          issueCategories.forEach((category) => {
            if (category && category.id) {
              resetCheckedState[category.id] = false;
            }
          });
        }
        setCheckedItems(resetCheckedState);
      }, 5000);
    }
  };

  return (
    <SideDrawer
      show={isRPDrawerOpen}
      title="Report Your Issue"
      style={{ gap: "1rem" }}
      className="rtp"
      isWhiteboardOpen={isWhiteboardOpen}
    >
      {problemReported ? (
        <div className="rtp-success">
          <span className="rtp-success-icon">
            <Lottie
              animationData={sendMessageAnimation}
              loop={false}
              style={{ width: 150, height: 150 }}
            />
          </span>
          <span className="rtp-success-message">
            {meetingFeatures?.configurations
              ? meetingFeatures?.configurations?.branding_enabled &&
                meetingFeatures?.configurations?.branding_app_title
                ? `Your concern has been sent to Team ${meetingFeatures?.configurations?.branding_app_title} and will be resolved soon!`
                : "Your concern has been sent to Team Daakia and will be resolved soon!"
              : "Your concern has been sent to Team Daakia and will be resolved soon!"}
          </span>
        </div>
      ) : (
        <>
          <p className="rtp-heading">Select your issue categories (max 3)</p>
          <div className="rtp-chips">
            {categoriesLoading ? (
              <p>Loading categories...</p>
            ) : Array.isArray(issueCategories) && issueCategories.length > 0 ? (
              issueCategories.map((category) => {
                const currentlySelected = Object.keys(checkedItems).filter(
                  (categoryId) => checkedItems[categoryId]
                ).length;
                const isDisabled =
                  !checkedItems[category.id] && currentlySelected >= 3;
                const isSelected = checkedItems[category.id] || false;

                return (
                  <button
                    key={category.id}
                    className={`rtp-chip ${isSelected ? 'selected' : ''} ${isDisabled ? 'disabled' : ''}`}
                    onClick={() => !isDisabled && onChange(category.id)}
                    disabled={isDisabled}
                    type="button"
                  >
                    {category.name}
                  </button>
                );
              })
            ) : (
              <p>No categories available</p>
            )}
          </div>
          {/* <hr className="rtp-hr" /> */}

          {/* Attachments Section */}
          <div className="rtp-attachments">
            <div className="rtp-attachments-header">
              <p>Attachments (max 5, 10MB each)</p>
              <Button
                type="default"
                size="small"
                onClick={handleFileSelect}
                disabled={attachments.length >= 5 || loading}
                className="rtp-add-btn"
                icon={
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path 
                      d="M21.44 11.05l-9.19 9.19a6 6 0 01-8.49-8.49l9.19-9.19a4 4 0 015.66 5.66L9.64 16.2a2 2 0 01-2.83-2.83l8.49-8.49"
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    />
                  </svg>
                }
              >
                Add File
              </Button>
            </div>

            {attachments.length > 0 && (
              <div className="rtp-attachments-list">
                {attachments.map((attachment) => (
                  <div key={attachment.id} className="rtp-attachment-item">
                    <div className="rtp-attachment-info">
                      <span className="rtp-attachment-name">
                        {attachment.filename}
                      </span>
                      <span className="rtp-attachment-size">
                        {formatFileSize(attachment.size)}
                      </span>
                    </div>
                    <Button
                      type="text"
                      size="small"
                      onClick={() => handleRemoveAttachment(attachment.id)}
                      disabled={loading}
                      danger
                    >
                      🗑️
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="rtp-other-issues">
            <p>
              Please describe your issue in detail.
            </p>
            <div className="rtp-input-box">
              <TextArea
                maxLength={500}
                showCount
                value={textAreaValue}
                onChange={handleTextChange}
                placeholder="Describe your issue..."
              />
            </div>

            {/* Email Section */}
            <div className="rtp-email-section">
              <div className="rtp-email-header">
                <p>Not signed in? Drop your email below</p>
                {/* <p>we&lsquo;ll update you once the issue is resolved</p> */}
                <Button
                  type="link"
                  size="small"
                  onClick={handleEmailToggle}
                  disabled={loading}
                  className="rtp-email-toggle"
                >
                  {emailEnabled ? 'saved' : 'edit'}
                </Button>
              </div>
              <div className="rtp-email-input">
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  value={emailValue}
                  onChange={handleEmailChange}
                  disabled={!emailEnabled || loading}
                  prefix={<MailOutlined style={{ color: '#ccc' }} />}
                  style={{
                    backgroundColor: '#2f2f2f',
                    color: '#fff',
                    border: '1px solid #555',
                    cursor: !emailEnabled ? 'not-allowed' : 'text'
                  }}
                />

                {!emailEnabled && (
                  <small className="rtp-email-help">
                    Email is pre-filled from your account if you are signed in.
                  </small>
                )}
              </div>
            </div>

            <div className="rtp-buttons">
              <Button
                type="primary"
                size="large"
                className="ls-button ls-button-cancel"
                ghost
                onClick={() => {
                  setOpenDrawer(null);
                }}
                disabled={loading}
              >
                CANCEL
              </Button>
              <Button
                type="primary"
                size="large"
                className="ls-button"
                onClick={handleSubmit}
                loading={loading}
              >
                SUBMIT
              </Button>
            </div>
          </div>
        </>
      )}
    </SideDrawer>
  );
}