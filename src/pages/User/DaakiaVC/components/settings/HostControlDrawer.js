/* eslint-disable */
import React from "react";
import { Switch } from "antd";
import SideDrawer from "../SideDrawer";
// import { publishAction } from "../../utils/reactions";
import { DataReceivedEvent } from "../../utils/constants";
import "../../styles/HostControlDrawer.scss";
import { VideoConferenceService } from "../../services/VideoConferenceServices";

export default function HostControlDrawer({
  showHostControl,
  room,
  setForceVideoOff,
  forceVideoOff,
  setForceMute,
  forceMute,
  setCanDownloadChatAttachment,
  canDownloadChatAttachment,
  setAllowLiveCollabWhiteBoard,
  allowLiveCollabWhiteBoard,
  isWhiteboardOpen,
  isScreenShareAllowedByHost,
  setIsScreenShareAllowedByHost,

  setToastNotification,
  setToastStatus,
  setShowToast,
}) {
  const onMuteChange = async (checked) => {
    const strData = JSON.stringify({
      action: DataReceivedEvent.FORCE_MUTE_ALL,
      value: checked,
    });
    const encoder = new TextEncoder();
    const data = encoder.encode(strData);
    await room.localParticipant.publishData(data, { reliable: true });
    // publishAction(room, {
    //   key: DataReceivedEvent.FORCE_MUTE_ALL,
    //   value: checked,
    // });
    setForceMute(checked);
  };

  const onVideoChange = async (checked) => {
    if (!room) return;
    const strData = JSON.stringify({
      action: DataReceivedEvent.FORCE_VIDEO_OFF_ALL,
      value: checked,
    });
    const encoder = new TextEncoder();
    const data = encoder.encode(strData);
    await room.localParticipant.publishData(data, { reliable: true });
    // publishAction(room, {
    //   key: DataReceivedEvent.FORCE_VIDEO_OFF_ALL,
    //   value: checked,
    // });
    setForceVideoOff(checked);
  };

  const chatAttachmentCheck = async (checked) => {
    if (!room) return;
    const strData = JSON.stringify({
      action: DataReceivedEvent.CAN_DOWNLOAD_CHAT_ATTACHEMENT,
      value: checked,
    });
    const encoder = new TextEncoder();
    const data = encoder.encode(strData);
    await room.localParticipant.publishData(data, { reliable: true });
    setCanDownloadChatAttachment(checked);
  };

  const liveCollabWhiteBoard = async (checked) => {
    if (!room) return;
    const strData = JSON.stringify({
      action: DataReceivedEvent.ALLOW_LIVE_COLLAB_WHITEBOARD,
      value: checked,
    });
    const encoder = new TextEncoder();
    const data = encoder.encode(strData);
    await room.localParticipant.publishData(data, { reliable: true });
    setAllowLiveCollabWhiteBoard(checked);
  };

  const webinarMode = (checked) => {
    onMuteChange(checked);
    onVideoChange(checked);
  };

  const updateScreenSharePermission = async (checked) => {
    const response = await VideoConferenceService.updateScreenSharePermission(
      room.roomInfo?.name,
      checked
    );
    if (response.success === 1) {
      setToastNotification("Screen share permission updated successfully");
      setToastStatus("success");
      setShowToast(true);

      // Send DataReceivedEvent only if success === 1
      const strData = JSON.stringify({
        action: DataReceivedEvent.ALLOW_SCREEN_SHARE_FOR_ALL,
        value: checked,
      });
      const encoder = new TextEncoder();
      const data = encoder.encode(strData);
      room.localParticipant.publishData(data, { reliable: true });
      setIsScreenShareAllowedByHost(checked);
    } else {
      setToastNotification("Failed to update screen share permission");
      setToastStatus("error");
      setShowToast(true);
    }
    return response;
  }

  return (
    <SideDrawer
      show={showHostControl}
      title="Host Control"
      className={`host-control`}
      isWhiteboardOpen={isWhiteboardOpen}
    >
      <div className="hc primary-font">
        <p className="hc-subhead">
          Stay in control of your meetings with advance host options
        </p>
        <div className="hc-component">
          <div className="hc-component-switch">
            <p>Webinar mode</p>
            <Switch
              onChange={webinarMode}
              checked={forceMute && forceVideoOff}
            />
          </div>
          <p>
            If turned ON, all participants except Hosts / CoHosts stay muted
            with their camera off.
          </p>
        </div>
        <div className="hc-comps">
          <div className="hc-component component-2">
            <div className="hc-component-switch">
              <p className="hc-comps-heading">Participants Audio</p>
              <Switch onChange={onMuteChange} checked={forceMute} />
            </div>
            <p className="hc-comps-desc">
              If turned off, participants can unmute themselves
            </p>
          </div>
          <div className="hc-component component-2">
            <div className="hc-component-switch">
              <p className="hc-comps-heading">Participants Video</p>
              <Switch onChange={onVideoChange} checked={forceVideoOff} />
            </div>
            <p className="hc-comps-desc">
              If turned off, participants can turn their camera on
            </p>
          </div>
        </div>
        <hr />
        <div className="hc-component" >
          <div className="hc-component-switch">
            <p>Allow Chat attachment download</p>
            <Switch
              onChange={chatAttachmentCheck}
              checked={canDownloadChatAttachment}
            />
          </div>
          <p>
            If turned ON, all participants will be able to download the
            attachments send in chat.
          </p>
        </div>
        <hr />
        <div className="hc-component" >
          <div className="hc-component-switch">
            <p>White Board Collab</p>
            <Switch
              onChange={liveCollabWhiteBoard}
              checked={allowLiveCollabWhiteBoard}
            />
          </div>
          <p>
            If turned ON, all participants will be able to draw on the whiteboard
          </p>
        </div>
        <hr />
        <div className="hc-component">
          <div className="hc-component-switch">
            <p>Allow Screen Share</p>
            <Switch
              onChange={updateScreenSharePermission}
              checked={isScreenShareAllowedByHost}
            />
          </div>
          <p>
            If turned ON, all participants will be able to share their screen without the permission of Host / Co-Host.
          </p>
        </div>
      </div>
    </SideDrawer>
  );
}
