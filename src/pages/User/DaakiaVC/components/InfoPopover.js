import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Popover } from "antd";
import { isMobileBrowser } from "@livekit/components-core";
import moment from "moment-timezone";
import { ReactComponent as InfoIcon } from "../assets/icons/InfoIcon.svg";
import { ReactComponent as CopyIcon } from "../assets/icons/Copy.svg";
import { encoder, baseUrlGenerator } from "../utils/helper";
import "../styles/InfoPopover.scss";
import "../styles/index.scss";
import { useSaasHelpers } from "../SaaS/helpers/helpers";

export function InfoPopover({
  id,
  meetingDetails,
  meetingFeatures,
  setToastNotification,
  setToastStatus,
  setShowToast,
  sipData,
}) {
  const [showPopover, setShowPopover] = useState(false);
  const [showDialingNo, setShowDialingNo] = useState(false);
  const baseUrl = baseUrlGenerator();
  const {
    saasMeetingConfigurations,
    saasBrandingEnabled,
    saasMeetingId,
    isSaaS,
  } = useSaasHelpers();
  const saasMeetingInviteUrl = `${baseUrl}/saas/meeting/invitee/${saasMeetingId}`;

  const copyMeetingLink = async () => {
    let meetingLink;
    if (isSaaS && saasMeetingId) {
      meetingLink = saasMeetingInviteUrl;
    } else {
      meetingLink = `${baseUrl}/v1/meeting/${encoder(id)}`;
    }
    try {
      await navigator.clipboard.writeText(meetingLink);
    } catch (err) {
      setToastNotification(err.message);
      setToastStatus("error");
      setShowToast(true);
    }
  };

  const copyEventDetails = async () => {
    let meetingLink;
    if (isSaaS && saasMeetingId) {
      meetingLink = saasMeetingInviteUrl;
    } else {
      meetingLink = `${baseUrl}/v1/meeting/${encoder(id)}`;
    }
    // Get the browser's time zone
    const browserTimeZone =
      Intl.DateTimeFormat().resolvedOptions().timeZone || "Asia/Kolkata";
    let timeString;
    let type = "Personal Meeting Room";
    const isPersonalRoom = meetingDetails.topic === type && meetingDetails.chapter === type && meetingDetails.subject === type;
    if (isPersonalRoom) {
      timeString = moment(meetingDetails.start_date)
        .tz(browserTimeZone)
        .format("HH:mm");
      let countryStandardTime = moment(meetingDetails.start_date)
        .tz(browserTimeZone)
        .format("z");
      timeString = `${timeString} ${countryStandardTime}`;
    } else {
      timeString = moment(meetingDetails.start_date)
        .tz(browserTimeZone)
        .format("HH:mm");
    }
    // Get the country standard time (e.g., IST, PST, etc.)
    let countryStandardTime = moment(meetingDetails.start_date)
      .tz(browserTimeZone)
      .format("z");
    if (meetingDetails.end_date) {
      if (!isPersonalRoom) {
        const endTime = moment(meetingDetails.end_date)
          .tz(browserTimeZone)
          .format("HH:mm");
        timeString = `${timeString} - ${endTime} ${countryStandardTime}`;
      }
    } else {
      timeString = `${timeString} ${countryStandardTime}`;
    }

    let phoneSection = "";
    if (sipData?.numbers && sipData.numbers.length > 0) {
      phoneSection = sipData.numbers
        .map((number) => {
          const [phone, country] = number?.formatted_phone.split(" (");
          return `${phone} (${country ? country.replace(")", "") : ""})`;
        })
        .join("\n");
    }
    const formattedText = 
`Event Name: ${meetingDetails.event_name}
Date: ${moment(meetingDetails.start_date)
  .tz("Asia/Kolkata")
  .format(
    "DD MMM YYYY"
)}
Time: ${timeString}
${isPersonalRoom ? "Type: Personal Room (Available anytime)\n" : ""}
${meetingLink}

Join by phone
${phoneSection}

Join by video system
Dial ${sipData?.sip_dialing_ip || ""}

PIN: ${sipData?.sip_pin || ""} (Enter this PIN after dialling in)`;
    try {
      await navigator.clipboard.writeText(formattedText);
      setToastNotification("Meeting details copied to clipboard");
      setToastStatus("success");
      setShowToast(true);
    } catch (err) {
      setToastNotification(err.message);
      setToastStatus("error");
      setShowToast(true);
    }
  };

  const content = (
    <div className="inf-parent-container primary-font">
      <div className="ind-title">
        {meetingDetails?.host}&apos;s{" "}
        {saasBrandingEnabled() &&
        saasMeetingConfigurations()?.branding_enabled === 1
          ? saasMeetingConfigurations()?.branding_app_title
          : "Daakia"}{" "}
        Meeting
      </div>
      {showDialingNo && (
        <Modal
          open={showDialingNo}
          onCancel={() => {
            setShowDialingNo(false);
            setShowPopover(false);
          }}
          footer={null}
          width={400}
          className="show-dialing-modal"
        >
          <div className="show-dialing">
            <h5>More ways to join:</h5>
            <div className="show-dialing-phone">
              <span className="show-dialing-heading show-dialing-heading-phone">
                Join by phone
              </span>
              {sipData?.numbers.map((number, index) => (
                <div
                  className="show-dialing-phone-number"
                  key={number?.formatted_phone || index}
                >
                  <ul>
                    <li>
                      {isMobileBrowser() ? (
                        <a
                          href={`tel:${number?.formatted_phone.split(" (")[0]}`}
                        >
                          {number?.formatted_phone.split(" (")[0]}
                        </a>
                      ) : (
                        <p>
                          {number?.formatted_phone.split(" (")[0]}{" "}
                          <span>({number?.formatted_phone.split(" (")[1]}</span>
                        </p>
                      )}
                    </li>
                  </ul>
                </div>
              ))}
            </div>
            <div className="show-dialing-ip">
              <span className="show-dialing-heading show-dialing-heading-video">
                Join by video system
              </span>
              <p>
                <span>Dial </span>&nbsp;&nbsp;{sipData?.sip_dialing_ip}
              </p>
            </div>
            <hr />
            <div className="show-dialing-phone-pin">
              <p>{`Enter the PIN ${sipData?.sip_pin}, followed by # after dialing in.`}</p>
            </div>
          </div>
        </Modal>
      )}
      <div className="inf-description">
        <div>
          <span>Host</span>
          <p>{meetingDetails?.host}</p>
        </div>
        <hr />
        <div>
          <div>
            <span>Meeting Name</span>
            <p>{meetingDetails.event_name}</p>
          </div>
        </div>
        <hr />
        <div>
          <span>Invitation Link</span>
          <a
            href={
              isSaaS && saasMeetingId
                ? saasMeetingInviteUrl
                : `${baseUrl}/v1/meeting/${encoder(id)}`
            }
          >
            {isSaaS && saasMeetingId
              ? `${baseUrl}/saas/meeting/invitee/${saasMeetingId}`
              : `${baseUrl}/v1/meeting/${encoder(id)}`}
          </a>
        </div>
      </div>
      <div className="inf-meeting-links">
        <Button onClick={() => copyMeetingLink()} type="round">
          <CopyIcon />
          <span>Copy Link</span>
        </Button>
        <Button onClick={() => copyEventDetails()} type="round">
          <CopyIcon />
          <span>Copy Invite</span>
        </Button>
        {meetingFeatures?.international_phone === 1 && (
          <Button
            onClick={() => {
              setShowDialingNo(!showDialingNo);
            }}
            type="round"
          >
            <span>SIP Dialing</span>
          </Button>
        )}
      </div>
    </div>
  );
  return (
    <div
      /* onClick={() => setShowPopover(!showPopover)} */ className="inf-button"
    >
      <Popover
        content={content}
        title={null}
        // trigger="click"
        // open={showPopover}
        onOpenChange={() => {
          setShowPopover(!showPopover);
        }}
        overlayInnerStyle={{
          backgroundColor: "#1e1e1e",
          width: "430px",
        }}
        overlayClassName="inf-popover"
        // close popover when showDialingNo is true
        open={showPopover && !showDialingNo}
        // onClick={() => setShowPopover(!showPopover)}
        // onClose={() => setShowPopover(!showPopover)}
      >
        <InfoIcon />
      </Popover>
    </div>
  );
}
