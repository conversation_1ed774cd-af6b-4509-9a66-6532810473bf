/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Track, VideoPreset } from "livekit-client";
import { Popover, Modal, Tabs, Switch } from "antd";
import { onDeviceError } from "../../utils/helper";
import { TrackToggle } from "../TrackToggle";
import { ReactComponent as ScreenShareIcon } from "../../assets/icons/Share.svg";
import DefaultScreenImg from "./Assets/DefaultScreen.png";

import "../../styles/ControlBar.scss";
import "../../styles/index.scss";
import "./ScreenShareMenuButton.scss";
import { DataReceivedEvent } from "../../utils/constants";
import { useSaasHelpers } from "../../SaaS/helpers/helpers";
import { VideoConferenceService } from "../../services/VideoConferenceServices";

export function ScreenShareMenuButton({
  onScreenShareChange,
  maxWidth,
  maxHeight,
  roomData,
  setScreenShareMode,
  meetingFeatures,
  setIsPIPEnabled,
  screenShareSources,
  isElectronApp,
  setScreenShareDisplayId,
  setToastNotification,
  setToastStatus,
  setShowToast,
  isScreenShareAllowedByHost,
  setIsScreenShareAllowedByHost,
  screenSharePermissions,
  localParticipantId,
  localParticipant,
  remoteParticipants,
  id,
}) {
  const [showPopover, setShowPopover] = useState(false);
  const [screenSourceId, setScreenSourceId] = useState(null);
  const [activeTabKey, setActiveTabKey] = useState("1");
  const [showScreenShareModal, setShowScreenShareModal] = useState(false);
  const [screensScourcesData, setScreensScourcesData] = useState([
    {
      id: "1",
      label: "Screen",
      sources: [],
    },
    {
      id: "2",
      label: "Application window",
      sources: [],
    },
  ]);

  // For default web mode
  const handleChangeMode = (mode) => {
    setScreenShareMode(mode);

    // Check if roomData and its options exist before trying to modify them
    if (roomData && roomData.options && roomData.options.publishDefaults) {
      if (mode === "video") {
        roomData.options.publishDefaults.screenShareEncoding = {
          maxBitrate: 1_800_000,
          maxFramerate: 30,
        };
        roomData.options.publishDefaults.screenShareSimulcastLayers = [
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 800_000,
              maxFramerate: 5,
            },
          },
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 400_000,
              maxFramerate: 1,
            },
          },
        ];
      } else if (mode === "text") {
        roomData.options.publishDefaults.screenShareEncoding = {
          maxBitrate: 1_000_000,
          maxFramerate: 5,
        };
        roomData.options.publishDefaults.screenShareSimulcastLayers = [
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 800_000,
              maxFramerate: 1,
            },
          },
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 400_000,
              maxFramerate: 1,
            },
          },
        ];
      }
    } else {
      console.warn('Room options not available for screen share configuration');
    }
    setIsPIPEnabled(true);
  };

  const role = JSON.parse(localParticipant?.metadata).role_name;
  // Find the moderator/host participant
  const hostParticipant = Array.from(remoteParticipants.values()).find(participant => {
    try {
      const metadata = JSON.parse(participant?.metadata || "{}");
      return metadata?.role_name === "moderator";
    } catch (error) {
      console.warn("Failed to parse participant metadata:", error);
      return false;
    }
  });
  const findCoHostParticipant = () => {
    // Find the co-host participant
    const coHostParticipant = Array.from(remoteParticipants.values()).find(participant => {
      try {
        const metadata = JSON.parse(participant?.metadata || "{}");
        return metadata?.role_name === "cohost";
      } catch (error) {
        console.warn("Failed to parse participant metadata:", error);
        return false;
      }
    });
    return coHostParticipant;
  };
  const handleScreenShareWithPermission = () => {
    if (
      role === "moderator" ||
      role === "cohost" ||
      isScreenShareAllowedByHost ||
      (screenSharePermissions && screenSharePermissions[localParticipantId])
    ) {
      if(meetingFeatures?.share_youtube === 1) {
        setShowPopover(true);
      } else if (isElectronApp) {
        showModal();
      } else {
        handleChangeMode("text");
      }
    } else {
      const encoder = new TextEncoder();
      const encodedMessage = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.REQUEST_SCREEN_SHARE_PERMISSION,
          request_by: localParticipant?.identity,
          request_by_name: localParticipant?.name,
        })
      );
      // Check if host participant exists and has a valid identity
      if (hostParticipant && hostParticipant.identity) {
        localParticipant.publishData(encodedMessage, {
          reliable: true,
          destinationIdentities: [hostParticipant.identity],
        });
        setToastNotification("Screen share request sent to the Host.");
        setToastStatus("info");
        setShowToast(true);
      } else if (findCoHostParticipant() && findCoHostParticipant().identity) {
        localParticipant.publishData(encodedMessage, {
          reliable: true,
          destinationIdentities: [findCoHostParticipant().identity],
        });
        setToastNotification("Screen share request sent to the Co-Host.");
        setToastStatus("info");
        setShowToast(true);
      } else {
        setToastNotification("No Host or Co-Host found, you can't share screen.");
        setToastStatus("warning");
        setShowToast(true);
      }
    }
  };

  const content = (
    <div className="primary-font ss-parent">
      <TrackToggle
        source={Track.Source.ScreenShare}
        // captureOptions={{ audio: true, selfBrowserSurface: "include" }}
        captureOptions={{
          audio: true,
          contentHint: "detail",
          resolution: {
            width: maxWidth,
            height: maxHeight,
            frameRate: 5,
          },
          selfBrowserSurface: "include",
        }}
        showIcon={false}
        onChange={onScreenShareChange}
        className="ss-button"
        onDeviceError={onDeviceError}
        onClick={() => handleChangeMode("text")}
      >
        Screen
      </TrackToggle>
      <TrackToggle
        source={Track.Source.ScreenShare}
        // captureOptions={{ audio: true, selfBrowserSurface: "include" }}
        captureOptions={{
          audio: true,
          contentHint: "detail",
          resolution: {
            width: maxWidth,
            height: maxHeight,
            frameRate: 30,
          },
          selfBrowserSurface: "include",
        }}
        showIcon={false}
        onChange={onScreenShareChange}
        className="ss-button"
        onDeviceError={onDeviceError}
        onClick={() => handleChangeMode("video")}
      >
        Video
      </TrackToggle>
    </div>
  );

  useEffect(() => {
    if (screenShareSources.length > 0) {
      setScreensScourcesData(null);
      const updatedData = screensScourcesData.map((item) => ({
        ...item,
        sources:
          item.label === "Screen"
            ? screenShareSources.filter((source) =>
                source.id.includes("screen")
              )
            : screenShareSources.filter(
                (source) => !source.id.includes("screen")
              ),
      }));

      setScreensScourcesData(updatedData);

      // Set default source ID to the first source in the initial tab
      if (updatedData[0].sources.length > 0 && screenSourceId === null) {
        setScreenSourceId(updatedData[0].sources[0].id);
      }
    }
  }, [screenShareSources]);

  // For electron mode
  const handleAppScreenShare = async () => {
    if (screenShareSources.length > 0) {
      if (screenSourceId !== null) {
        let stream;

        try {
          // Request screen capture
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              mandatory: {
                chromeMediaSource: "desktop",
                // chromeMediaSourceId: source.id,
                chromeMediaSourceId: screenSourceId,
                maxWidth: 1920,
                maxHeight: 1080,
              },
            },
            // audio: screenAudio
            //   ? {
            //       mandatory: {
            //         chromeMediaSource: "desktop", // Screen audio source
            //         chromeMediaSourceId: screenSourceId, // Match the audio source to the specific app
            //       },
            //     }
            //   : false,
            audio: false
          });
        } catch (error) {
          setToastNotification(error.message);
          setToastStatus("error");
          setShowToast(true);
          // console.error("Error capturing screen:", error);
          return;
        }

        // Check if stream was successfully obtained
        if (stream) {
          // Get the video and audio tracks from the stream
          const track = stream.getVideoTracks()[0];
          // const audioTrack = stream.getAudioTracks()[0];
          if (!track) {
            console.error("No video track found");
            return;
          }

          const { width, height } = track.getSettings();

          try {
            // Publish the track with simulcast options
            await localParticipant.publishTrack(track, {
              simulcast: true,
              screenShareSimulcastLayers: [
                new VideoPreset(width || 0, height || 0, 500000, 5),
                new VideoPreset(width || 0, height || 0, 200000, 1),
              ],
              source: Track.Source.ScreenShare,
            });
            setShowToast(true);
            setToastNotification("Screen sharing started successfully!");
            setToastStatus("success");
          } catch (publishError) {
            console.error("Error publishing track:", publishError);
          }
        }
      }
      onScreenShareChange(true);
    } else {
      setToastNotification("No screen share sources available.");
      setToastStatus("warning");
      setShowToast(true);
      // console.error("No screen share sources available.");
    }
  };

  // Function to handle source selection
  const handleSelectSource = (source) => {
    // Implement logic to start screen sharing with the selected source
    if (source.displayId && source.displayId !== "") {
      // Convert displayId to a number and set it
      const numericDisplayId = Number(source.displayId);
      if (!Number.isNaN(numericDisplayId)) {
        setScreenShareDisplayId(numericDisplayId);
      }
    } else {
      setScreenShareDisplayId(1);
    }
    setScreenSourceId(source.id);
  };

  const handleTabChange = (key) => {
    setActiveTabKey(key);
    const newActiveTabSources = screensScourcesData.find(
      (tab) => tab.id === key
    ).sources;

    // Set `screenSourceId` to the first source in the new tab if available
    if (newActiveTabSources.length > 0) {
      setScreenSourceId(newActiveTabSources[0].id);
    } else {
      setScreenSourceId(null); // No sources in the new tab
    }
  };

  // Helper function for safe Base64 encoding
  const toBase64 = (arrayBuffer) => {
    let binary = "";
    const bytes = new Uint8Array(arrayBuffer);
    for (let i = 0; i < bytes.length; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  };

  // Define tab items
  const tabItems = screensScourcesData.map((tab) => ({
    key: tab.id,
    label: tab.label,
    children:
      tab.sources.length > 0 &&
      tab.sources.map((sourceItem) => (
        <div
          className={`${
            screenSourceId === sourceItem.id
              ? "selected-screen-share d-flex"
              : ""
          } screen-share-option`}
          onClick={() => handleSelectSource(sourceItem)}
          key={sourceItem.id} // Adding a unique key for React rendering
        >
          {sourceItem.thumb.length > 0 ? (
            <img
              src={`data:image/png;base64,${toBase64(sourceItem.thumb)}`}
              alt={sourceItem.name}
              onError={(e) => {
                // Fallback to the default image on error
                e.target.onerror = null; // Prevent infinite loop if fallback also fails
                e.target.src = DefaultScreenImg; // Assuming DefaultScreenImg is a URL or base64 string
              }}
            />
          ) : (
            <img src={DefaultScreenImg} alt="Default Screen" />
          )}
          <p>
            {sourceItem.name.length > 15
              ? `${sourceItem.name.substring(0, 15)}...`
              : sourceItem.name}
          </p>
        </div>
      )),
  }));

  const showModal = () => {
    setShowScreenShareModal(true);
  };

  const handleOk = () => {
    handleAppScreenShare();
    setShowScreenShareModal(false);
  };

  const handleCancel = () => {
    setShowScreenShareModal(false);
  };

  const contentElectron = (
    <>
      <div className="primary-font ss-parent">
        <div
          className="ss-button"
          onClick={() => {
            showModal();
            setShowPopover(false);
          }}
        >
          Screen
        </div>
        <div
          className="ss-button"
          onClick={() => {
            showModal();
            setShowPopover(false);
          }}
        >
          Video
        </div>
      </div>
    </>
  );

  // getScreenSharePermission
  const getScreenSharePermission= async() => {
    const response = await VideoConferenceService.getScreenSharePermission(
      id
    );
    console.log("response", response);
    if (response.success === 1) {
      setIsScreenShareAllowedByHost(response.data?.screen_share_consent);
    }
    return response;
  }

  useEffect(() => {
    getScreenSharePermission();
  }, []);

  return (
    <>
      {meetingFeatures?.share_youtube === 1 ? (
        <Popover
          content={isElectronApp ? contentElectron : content}
          title={null}
          trigger="click"
          open={showPopover}
          onOpenChange={(open) => {
            if (!open) {
              setShowPopover(false);
            }
          }}
          // onOpenChange={() => setShowPopover(!showPopover)}
          overlayInnerStyle={{
            borderRadius: "10px",
          }}
          overlayClassName="screen-share-popover"
        >
          <div 
            className="lk-button control-bar-button control-bar-button-icon lk-button-group-buttons"
            onClick={(e)=>{
              if (!(role === "moderator" || role === "cohost")) {
                e.preventDefault();
                e.stopPropagation();
              }
              handleScreenShareWithPermission();
            }}
          >
            <ScreenShareIcon />
          </div>
        </Popover>
      ) : isElectronApp ? (
        <div
          className="lk-button control-bar-button control-bar-button-icon lk-button-group-buttons"
          onClick={(e)=>{
            if (!(role === "moderator" || role === "cohost") && !screenSharePermissions[localParticipantId]) {
              e.preventDefault();
              e.stopPropagation();
            }
            handleScreenShareWithPermission();
          }}
        >
          <ScreenShareIcon 
            onClick={(e)=>{
              if (!(role === "moderator" || role === "cohost") && !screenSharePermissions[localParticipantId] && !isScreenShareAllowedByHost) {
                e.preventDefault();
                e.stopPropagation();
              }
              handleScreenShareWithPermission();
            }}
          />
        </div>
      ) : (
        <TrackToggle
          source={Track.Source.ScreenShare}
          captureOptions={{
            audio: true,
            contentHint: "detail",
            resolution: {
              width: maxWidth,
              height: maxHeight,
              frameRate: 5,
            },
            selfBrowserSurface: "include",
          }}
          showIcon={false}
          onChange={onScreenShareChange}
          className="lk-button control-bar-button control-bar-button-icon lk-button-group-buttons"
          onDeviceError={onDeviceError}
          onClick={() => handleChangeMode("text")}
        >
          <div className="screen-share-icon"
            onClick={(e)=>{
              if (!(role === "moderator" || role === "cohost") && !screenSharePermissions[localParticipantId] && !isScreenShareAllowedByHost) {
                e.preventDefault();
                e.stopPropagation();
              }
              handleScreenShareWithPermission();
            }}
          >
            <ScreenShareIcon />
          </div>
        </TrackToggle>
      )}

      {/* Screen Share Modal */}
      {showScreenShareModal && (
        <Modal
          title="Screen Share"
          open={showScreenShareModal}
          onOk={handleOk}
          onCancel={handleCancel}
          okText="Start Sharing"
          cancelText="Cancel"
          className="screen-share-modal"
        >
          <Tabs
            items={tabItems}
            activeKey={activeTabKey}
            onChange={handleTabChange}
            className={`screen-share-tabs`}
          />
          {/* <div className="screen-share-audio">
            <span>Share System Audio : </span>
            <Switch
              checked={screenAudio}
              onChange={(checked) => setScreenAudio(checked)}
            />
          </div> */}
        </Modal>
      )}
    </>
  );
}
