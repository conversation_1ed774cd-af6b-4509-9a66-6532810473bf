@import "./variables";

.rtp{
  .sd-container-below{
    padding-top: 0.5rem;
  }
}
.rtp-success{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;

  &-message{
    text-align: center;
    margin-bottom: 5rem;
  }
}
.rtp-heading {
  color: #0a84ffe1;
  margin-top: -0.2rem;

  font-family: $font;
  font-size: .7rem;
  font-weight: 600;
  // line-height: 26.6px;
  text-align: left;
  margin-bottom: 0.2rem;
}

// Chip-style buttons instead of checkboxes
.rtp-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;

  .rtp-chip {
    padding: 0.25rem 0.5rem;
    border: 1px solid #e0e0e032;
    border-radius: 20px;
    background: transparent;
    color: #e2d9d991;
    font-family: $font;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    outline: none;
    min-width: fit-content;
    white-space: nowrap;

    &:hover:not(.disabled) {
      border-color: #0a84ff;
      background: rgba(10, 132, 255, 0.1);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(10, 132, 255, 0.2);
    }

    &.selected {
      background: #0a84ff;
      border-color: #0a84ff;
      color: white;
      box-shadow: 0 2px 8px rgba(10, 132, 255, 0.3);

      &:hover {
        background: #0071e3;
        border-color: #0071e3;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(10, 132, 255, 0.4);
      }
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      border-color: #ccc;
      color: #999;
      background: #f5f5f5;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }

    &:active:not(.disabled) {
      transform: translateY(0);
    }
  }
}

.rtp-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-left: 1.3rem;

  .ant-checkbox-wrapper {
    margin-left: 0;
    color: white;
  }
  p {
    font-family: $font;
    font-size: 16px;
    font-weight: 400;
    line-height: 26.6px;
    text-align: left;
    margin-bottom: 0;
  }
  span {
    font-family: $font;
    font-size: 12px;
    font-weight: 400;
    line-height: 18.62px;
    text-align: left;
  }
}
.rtp-hr {
  border: 1px solid white;
  margin: 0.8rem 0 !important;
}

// Email section styles
.rtp-email-section {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);

  .rtp-email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;

    p {
      margin: 0;
      font-family: $font;
      font-size: 13px;
      font-weight: 500;
      color: white;
      line-height: 1.4;
      padding-right: 0.5rem;
    }

    .rtp-email-toggle {
      color: #0a84ff !important;
      font-size: 12px !important;
      padding: 0 !important;
      height: auto !important;
      border: none !important;
      background: transparent !important;
      text-decoration: underline;

      &:hover {
        color: #0071e3 !important;
        text-decoration: none;
      }

      &:disabled {
        color: #999 !important;
        cursor: not-allowed;
      }
    }
  }

  .rtp-email-input {
    position: relative;

    .ant-input {
      background: transparent;
      color: rgba(255, 255, 255, 0.588);
      font-family: $font;
      font-size: 14px;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      padding-top: 0.1rem;
      padding-bottom: 0.1rem;
      transition: all 0.2s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.156);
      }

      &:focus {
        border-color: #090909;
        box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.2);
      }

      &:disabled {
        border-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.5);
        cursor: not-allowed;
      }
    }

    .rtp-email-help {
      display: block;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.264);
      margin-top: 0.5rem;
      font-style: italic;
      font-family: $font;
    }
  }
}

.rtp-other-issues {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 0.6rem;
  p {
    font-family: $font;
    font-size: 15px;
    font-weight: 400;
    line-height: 21.28px;
    text-align: left;
    color: rgba(255, 255, 255, 0.89);
    margin-bottom: 0;
  }
  .rtp-input-box {
    position: relative;
    width: 100%;
    margin-top: 0.6rem;
    textarea {
      min-height: 120px;
      background-color: #2f2f2f;
      color: rgba(255, 255, 255, 0.863);
      font-size: 14px;
      border: none;
      border-radius: 10px;
      resize: none;
      position: relative;
      p {
        position: absolute;
        right: 0;
        bottom: 0;
        color: white;
        font-size: 12px;
      }
    }
    p {
      color: white;
      position: absolute;
      right: 6px;
      bottom: 0;
    }
  }
  .rtp-buttons {
    display: flex;
    justify-content: space-around;
    width: 100%;
    
    button{
      border-radius: 6px;
    }

    button:nth-child(2) {
      background-color: #0a84ff;
    }
  }
}
.rtp-attachments {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  
  .rtp-attachments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.8rem;

    
    p {
      margin: 0;
      font-size: 12px;
      color: #4b4b4b;
      font-weight: 500;
    }
  }
  
  .rtp-attachments-list {
    border-radius: 4px;
    max-height: 140px;
    overflow-y: auto;
    margin-bottom: 1rem;
  }
  
  .rtp-attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem;
    padding-left: 1rem;
    padding-right: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    background-color: #ffffff17; // optional


    
    .rtp-attachment-info {
      display: flex;
      flex-direction: column;
      flex: 1;
      
      .rtp-attachment-name {
        font-size: 0.7rem;
        font-weight: 500;
        color: #5c5c5c;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
      }
      
      .rtp-attachment-size {
        font-size: 0.5rem;
        color: #8888889d;
      }
    }
  }
}

.rtp-add-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 6px 12px !important;
  height: 32px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2) !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3) !important;
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2) !important;
  }
  
  &:disabled {
    background: #f5f5f5 !important;
    color: #bfbfbf !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
  }
  
  .anticon {
    display: flex !important;
    align-items: center !important;
  }
  
  svg {
    width: 14px !important;
    height: 14px !important;
  }
}

@media screen and (max-height: 570px) {
  .rtp-heading{
    font-size: 14px;
  }
  .rtp-checkboxes p{
    font-size: 14px;
  }
  .rtp-other-issues p{
    font-size: 12px;
  }
  .rtp-input-box textarea{
    min-height: 100px;
    font-size: 12px;

  }
  .rtp-buttons{
    .ant-btn{
      padding: 3px 10px;
      >span{
        font-size: 14px;
      }
    }
  } 
}