@import "./variables";

.sd-container{
  width: 27rem;
  width: auto;
  overflow: hidden;
  transform: translateX(100%);
  font-family: $font;
  &-whiteboard-open{
    padding: 0 !important;
    padding-left: 0.5rem !important;
  }
  &.show{
    // width: auto;
    transform: translateX(0);
    .sd-container-inner{
      animation: slide-side 0.2s ease-in;
    }
  }
}
@keyframes slide-side {
  0% {
    transform: translateX(100%);
    opacity: 0;
    visibility: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
    visibility: 1;
  }
}

.sd-container {
  height: 100%;
  padding: var(--lk-grid-gap);
  padding-left: 0;
  width: 440px;
  &::-webkit-scrollbar {
    width: 0;
  }

  .sd-container-inner {
    display: flex;
    flex-direction: column;
    background-color: #000;
    height: 100%;
    border-radius: 10px;
    padding: 10px;
    overflow-x: hidden;
    position: relative;
    hr {
      margin: 0;
    }
    .sd-header {
      display: flex;
      justify-content: space-between;
      position: sticky;
      top: 0rem;
      z-index: 10;
      background-color: #000;
      padding-bottom: 0.3rem;
      border-bottom: 1px solid rgb(255, 255, 255);

      .sd-title {
        display: flex;
        justify-content: space-between;
        align-self: center;
        // padding-right: 1rem;
        font-size: 1.2rem;
        font-weight: 400;
        width: 100%;
        text-align: start;
        color: rgba(255, 255, 255, 0.856);
        font-family: $font;
        position: relative;
        span{
          font-family: $font;
        }
      }
      .sd-button {
        // height: 40px;
        // width: 40px;
        border-radius: 50%;
        background-color: transparent;
        padding: 2px 10px;
        svg{
          width: 21px;
          height: 21px;
        }
      }
    }
    .sd-container-below {
      display: flex;
      flex-direction: column;
      overflow-x: hidden;
      overflow-y: auto;
      // height: 100%;
      position: relative;
      height: 100%;
      padding-right: 0.5rem;
      &-live-captions{
        padding-right: 0;
      }
      &::-webkit-scrollbar{
        width: 6px;
      }
      &::-webkit-scrollbar-thumb{
        background-color: #4d4d4d;
        border-radius: 10px;
      }
      &::-webkit-scrollbar-track{
        background-color: #000;
      }
    }
  }
}

.side-drawer-mobile{
  .sd-container{
    position: absolute;
    width: 100%;
    height: 100svh;
    // height: 100%;
    top: 0;
    left: 1%;
    transform: translateY(100%);
    opacity: 0;
    visibility: hidden;
    z-index: 11;
  }
  .show {
    animation: slide-up 0.5s forwards;
  }
}
.chat-close-ico{
  position: absolute;
  right: -0.3rem;
  top: -0.3rem;
}
@media screen and (max-width: 450px){
  .chat-drawer{
    position: absolute;
    left: 0;
    width: 100%;
    max-height: 100svh;
    top: 0;
    margin: 0px;
    z-index: 11;
  }
  .show-chat-mobile{
    animation: slide-up 0.5s forwards;
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(100%);
    opacity: 0;
    visibility: visible;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
}
