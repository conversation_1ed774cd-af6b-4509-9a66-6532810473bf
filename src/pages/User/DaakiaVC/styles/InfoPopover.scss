@import "./variables";

.inf-parent-container {
  width: 35vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  gap: 0.5rem;
  .ind-title {
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 1rem;
    font-family: $font;
    margin: 0;
  }
  .inf-description {
    font-size: 1rem;
    font-weight: 400;
    margin-bottom: 1rem;
    width: 100%;
  }
  .inf-meeting-link {
    font-size: 0.75rem;
    font-weight: 600;
    width: 85%;
    text-align: center;
    color: #0a84ff;
  }
}

.inf-button {
  padding: 8px 10px;
  cursor: pointer;
  svg {
    width: 20px;
    height: 20px;
  }
}

.show-dialing-phone{
  display: flex;
  flex-direction: column;
  &-number{
    display: flex;
    gap: 1rem;
    ul{
      margin: 0;
    }
  }
  &-pin{
    display: flex;
    gap: 1rem;
    margin-top: 0.3rem;
    p{
      font-size: 17px;
      span{
        color: #0a84ff;
      }
    }
    span{
      width: auto;
      font-family: "Inter";
      font-size: 14px;
      font-weight: 400;
      color: #818088;
      text-wrap: nowrap;
    }
  }
}

.inf-popover {
  .ant-popover-inner{
    border-radius: 10px;
  }
  :where(.ant-popover-inner-content){
    :where(.inf-parent-container) {
      width: auto;
      align-items: flex-start;
      border-radius: 24px;
      padding: 10px 12px;
      .show-dialing{
        width: 100%;
        height: auto;
        &-heading{
          font-family: $font;
          font-size: 1.1rem;
          font-weight: 500;
        }
        hr{
          margin: 0.4rem 0;
        }
        &-phone{
          display: flex;
          gap: 0.5rem;
          margin-top: 0.3rem;
          &:nth-child(1){
            margin-top: 1rem;
          }
          span{
            width: auto;
            font-family: "Inter";
            font-size: 14px;
            font-weight: 400;
            color: #818088;
            text-wrap: nowrap;
          }
          p{
            color: #d9d9d9;
            font-family: "Inter";
            font-size: 15px;
            font-weight: 500;
            text-align: left;
            margin: 0;
          }
        }
        &-numbers{
          display: flex;
          gap: 0.5rem;
        }
      }
    }
    :where(.ant-popover-inner) {
      border-radius: 14px;
      border: 1px solid #d9d9d96b;
      width: 450px !important;
    }
  }
}

.inf-description {
  position: relative;

  hr {
    margin: 0.5rem 0;
  }

  div {
    display: flex;
    align-items: center;
    gap: 1rem;
    &:nth-of-type(2) {
      justify-content: space-between;
    }
    span {
      width: auto;
      font-family: $font;
      font-size: 14px;
      font-weight: 400;
      color: #818088;
      text-wrap: nowrap;
    }
    a{
      display: block;
      word-break: break-all;
      word-wrap: break-word;
      overflow-wrap: break-word;
      max-width: 100%;
      color: #0a84ff;
      transition: all ease-in 0.1s;
      text-decoration: none;
      &:hover{
        color: #0b6ccd;
      }
    }
  }


  p {
    color: #d9d9d9;
    font-family: $font;
    font-size: 15px;
    font-weight: 500;
    text-align: left;
    margin: 0;
  }
}

.inf-meeting-links {
  display: flex;
  justify-content: space-around;
  width: 100%;
  gap: 0.4rem;

  button {
    background-color: #000;
    color: white;
    width: 140px;
    height: 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-family: $font;
    padding: 0 12px;

    &:hover {
      background-color: #1c1c1e;
      color: #fff !important;
    }

    &:focus{
      background-color: #000;
      color: white;
    }

    svg {
      height: 20px;
      width: 20px;
      color: rgb(41, 41, 41);
    }
  }
}
.show-dialing{
  display: flex;
  flex-direction: column;
  &-ip{
    margin-top: 0.5rem;
    p{
      font-size: 17px;
      margin: 0;
      margin-top: 0.3rem;
      font-family: $font;
      font-size: 500;
      span{
        color: #0a84ff;
      }
    }
  }
  &-heading{
    font-family: $font;
    font-size: 18px;
    font-weight: 600;
    &-phone{
      margin-bottom: 0.5rem;
    }
    &-video{
      margin-top: 0.5rem !important;
    }
  }
  &-link{
    font-size: 17px;
  }
  &-phone{
    margin-top: 0.5rem;
    &-number{
      ul{
        li{
          p{
            margin: 0;
            font-size: 17px;
          }
        }
      }
    }
  }
  h5{
    font-size: 22px;
    font-weight: 600;
    font-family: $font;
  }
  &-modal{
  width: 35rem !important;
  .ant-modal-content{
    border-radius: 10px;
    .ant-modal-header{
      border-radius: 10px 10px 0 0;
      padding-top: 1rem;
      .ant-modal-title{
        font-size: 20px;
        font-weight: 600;
        font-family: $font;
      }
    }
    .ant-modal-body{
      // padding-top: 0;
    }
  }
}}