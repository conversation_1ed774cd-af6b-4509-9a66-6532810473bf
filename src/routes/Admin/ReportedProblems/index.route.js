import { t } from "i18next";
import { Outlet } from "react-router-dom";
import { ReportedProblems, ReportById, ReportStatuses, ReportedIssueCategories } from "../../../pages/Admin";
import adminRoutesMap from "../../../routeControl/adminRoutes";

export default function route() {

  return [
    {
      path: adminRoutesMap.REPORTED_PROBLEMS.path,
      private: true,
      name: t("text.reportedProblems.title"),
      key: adminRoutesMap.REPORTED_PROBLEMS.path,
      belongsToSidebar: true,
      icon: adminRoutesMap.REPORTED_PROBLEMS.icon,
      element: <Outlet />,
      children: [
        {
          path: adminRoutesMap.REPORTS.path,
          private: true,
          name: t("text.reportedProblems.reports"),
          key: adminRoutesMap.REPORTS.path,
          belongsToSidebar: true,
          element: <ReportedProblems />,
        },
        {
          path: `${adminRoutesMap.REPORTS.path}/:reportId`,
          private: true,
          name: t("text.reportedProblems.reports"),
          key: `${adminRoutesMap.REPORTS.path}/:reportId`,
          belongsToSidebar: false,
          element: <ReportById />,
        },
        {
          path : adminRoutesMap.REPORTSTATUSES.path,
          private: true,
          name: t("text.reportedProblems.reportStatuses"),
          key: adminRoutesMap.REPORTSTATUSES.path,
          belongsToSidebar: true,
          element : <ReportStatuses />,
        },
        {
          path : adminRoutesMap.REPORTED_ISSUE_CATEGORIES.path,
          private: true,
          name: t("text.reportedProblems.reportedIssueCategories"),
          key: adminRoutesMap.REPORTED_ISSUE_CATEGORIES.path,
          belongsToSidebar: true,
          element: <ReportedIssueCategories/>,
        }
      ],
    },
  ];
}